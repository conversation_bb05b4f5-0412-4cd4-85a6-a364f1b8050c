{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Dialog,DialogTitle,DialogContent,DialogActions,Button,TextField,Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Paper,Typography,InputAdornment,Avatar,useTheme}from'@mui/material';import SearchIcon from'@mui/icons-material/Search';import AddIcon from'@mui/icons-material/Add';import PhoneIcon from'@mui/icons-material/Phone';import EmailIcon from'@mui/icons-material/Email';import LocationOnIcon from'@mui/icons-material/LocationOn';import{customerService}from'../../services/customer/customerService';import{LoadingSpinner,ErrorAlert}from'../common';import CustomerForm from'./CustomerForm';import{parseDate}from'../../utils/dateUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CustomerDialog(_ref){let{open,onClose,onSelectCustomer}=_ref;const[customers,setCustomers]=useState([]);const[filteredCustomers,setFilteredCustomers]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[searchTerm,setSearchTerm]=useState('');const[showAddCustomerForm,setShowAddCustomerForm]=useState(false);const theme=useTheme();useEffect(()=>{const fetchCustomers=async()=>{try{const data=await customerService.getAllCustomers();// Sắp xếp theo thời gian tạo mới nhất\nconst sortedData=[...data].sort((a,b)=>{const dateA=parseDate(a.createdAt);const dateB=parseDate(b.createdAt);return dateB.getTime()-dateA.getTime();});setCustomers(sortedData);setFilteredCustomers(sortedData);}catch(err){setError(err.message||'Đã xảy ra lỗi khi tải danh sách khách hàng');}finally{setLoading(false);}};if(open){fetchCustomers();}},[open]);const handleSearch=async()=>{if(!searchTerm.trim()){setFilteredCustomers(customers);return;}try{setLoading(true);// Tìm kiếm theo cả tên và số điện thoại\nconst results=await customerService.searchCustomers(searchTerm,searchTerm);setFilteredCustomers(results);if(results.length===0){setError('Không tìm thấy khách hàng nào phù hợp');}else{setError(null);}}catch(err){console.error('Error searching customers:',err);setError(err.message||'Đã xảy ra lỗi khi tìm kiếm khách hàng');}finally{setLoading(false);}};const handleSearchChange=e=>{setSearchTerm(e.target.value);if(!e.target.value.trim()){setFilteredCustomers(customers);}};// Xử lý phím Enter đã được chuyển sang onKeyDown trực tiếp\nconst handleAddCustomer=()=>{setShowAddCustomerForm(true);};// Xử lý sau khi thêm khách hàng mới\nconst handleCustomerAdded=async _newCustomer=>{setShowAddCustomerForm(false);setLoading(true);try{const data=await customerService.getAllCustomers();// Sắp xếp theo thời gian tạo mới nhất\nconst sortedData=[...data].sort((a,b)=>{const dateA=parseDate(a.createdAt);const dateB=parseDate(b.createdAt);return dateB.getTime()-dateA.getTime();});setCustomers(sortedData);setFilteredCustomers(sortedData);}catch(err){setError(err.message||'Đã xảy ra lỗi khi tải lại danh sách khách hàng');}finally{setLoading(false);}};const handleSelectCustomer=customer=>{onSelectCustomer(customer);onClose();};if(showAddCustomerForm){return/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:onClose,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Th\\xEAm kh\\xE1ch h\\xE0ng m\\u1EDBi\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(CustomerForm,{onSave:handleCustomerAdded,onCancel:()=>setShowAddCustomerForm(false)})})]});}return/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:onClose,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{borderBottom:\"1px solid \".concat(theme.palette.divider),pb:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:'bold',color:theme.palette.primary.main},children:\"Ch\\u1ECDn kh\\xE1ch h\\xE0ng\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(AddIcon,{}),onClick:handleAddCustomer,sx:{borderRadius:'20px',px:2},children:\"Th\\xEAm kh\\xE1ch h\\xE0ng\"})]})}),/*#__PURE__*/_jsxs(DialogContent,{sx:{pt:3},children:[error&&/*#__PURE__*/_jsx(ErrorAlert,{message:error}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3,display:'flex',gap:1},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:\"T\\xECm ki\\u1EBFm theo t\\xEAn ho\\u1EB7c s\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\",value:searchTerm,onChange:handleSearchChange,onKeyDown:e=>e.key==='Enter'&&handleSearch(),InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(SearchIcon,{color:\"action\"})})},sx:{'& .MuiOutlinedInput-root':{borderRadius:'20px'}}}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:handleSearch,sx:{borderRadius:'20px',px:3},children:\"T\\xECm ki\\u1EBFm\"})]}),loading?/*#__PURE__*/_jsx(LoadingSpinner,{}):/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Box,{sx:{mb:2},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[\"Hi\\u1EC3n th\\u1ECB \",filteredCustomers.length,\" kh\\xE1ch h\\xE0ng (s\\u1EAFp x\\u1EBFp theo th\\u1EDDi gian t\\u1EA1o m\\u1EDBi nh\\u1EA5t)\"]})}),/*#__PURE__*/_jsx(TableContainer,{component:Paper,sx:{borderRadius:'8px',boxShadow:'0 2px 8px rgba(0,0,0,0.05)','& .MuiTableCell-head':{backgroundColor:theme.palette.primary.light,color:theme.palette.primary.contrastText,fontWeight:'bold'}},children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"T\\xEAn kh\\xE1ch h\\xE0ng\"}),/*#__PURE__*/_jsx(TableCell,{children:\"C\\xF4ng ty\"}),/*#__PURE__*/_jsx(TableCell,{children:\"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Email\"}),/*#__PURE__*/_jsx(TableCell,{children:\"\\u0110\\u1ECBa ch\\u1EC9\"}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:\"Thao t\\xE1c\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:filteredCustomers.length===0?/*#__PURE__*/_jsx(TableRow,{children:/*#__PURE__*/_jsx(TableCell,{colSpan:6,align:\"center\",children:/*#__PURE__*/_jsxs(Box,{sx:{py:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"Kh\\xF4ng t\\xECm th\\u1EA5y kh\\xE1ch h\\xE0ng n\\xE0o\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:1},children:\"Th\\u1EED t\\xECm ki\\u1EBFm v\\u1EDBi t\\u1EEB kh\\xF3a kh\\xE1c ho\\u1EB7c th\\xEAm kh\\xE1ch h\\xE0ng m\\u1EDBi\"})]})})}):filteredCustomers.map(customer=>/*#__PURE__*/_jsxs(TableRow,{hover:true,sx:{cursor:'pointer','&:hover':{backgroundColor:theme.palette.action.hover}},onClick:()=>handleSelectCustomer(customer),children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Avatar,{sx:{bgcolor:theme.palette.primary.main,width:32,height:32,mr:1,fontSize:'0.9rem'},children:customer.fullName.charAt(0).toUpperCase()}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'medium'},children:customer.fullName})]})}),/*#__PURE__*/_jsx(TableCell,{children:customer.companyName||'-'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(PhoneIcon,{fontSize:\"small\",sx:{mr:0.5,color:theme.palette.text.secondary}}),customer.phoneNumber]})}),/*#__PURE__*/_jsx(TableCell,{children:customer.email?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(EmailIcon,{fontSize:\"small\",sx:{mr:0.5,color:theme.palette.text.secondary}}),customer.email]}):'-'}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(LocationOnIcon,{fontSize:\"small\",sx:{mr:0.5,color:theme.palette.text.secondary}}),customer.address]})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",size:\"small\",onClick:e=>{e.stopPropagation();handleSelectCustomer(customer);},sx:{borderRadius:'20px',px:2},children:\"Ch\\u1ECDn\"})})]},customer.id))})]})})]})]}),/*#__PURE__*/_jsx(DialogActions,{sx:{borderTop:\"1px solid \".concat(theme.palette.divider),p:2},children:/*#__PURE__*/_jsx(Button,{onClick:onClose,color:\"primary\",variant:\"outlined\",sx:{borderRadius:'20px',px:3},children:\"\\u0110\\xF3ng\"})})]});};export default CustomerDialog;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Typography", "InputAdornment", "Avatar", "useTheme", "SearchIcon", "AddIcon", "PhoneIcon", "EmailIcon", "LocationOnIcon", "customerService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CustomerForm", "parseDate", "jsx", "_jsx", "jsxs", "_jsxs", "CustomerDialog", "_ref", "open", "onClose", "onSelectCustomer", "customers", "setCustomers", "filteredCustomers", "setFilteredCustomers", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "showAddCustomerForm", "setShowAddCustomerForm", "theme", "fetchCustomers", "data", "getAllCustomers", "sortedData", "sort", "a", "b", "dateA", "createdAt", "dateB", "getTime", "err", "message", "handleSearch", "trim", "results", "searchCustomers", "length", "console", "handleSearchChange", "e", "target", "value", "handleAddCustomer", "handleCustomerAdded", "_newCustomer", "handleSelectCustomer", "customer", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "onSave", "onCancel", "sx", "borderBottom", "concat", "palette", "divider", "pb", "display", "justifyContent", "alignItems", "variant", "fontWeight", "color", "primary", "main", "startIcon", "onClick", "borderRadius", "px", "pt", "mb", "gap", "placeholder", "onChange", "onKeyDown", "key", "InputProps", "startAdornment", "position", "component", "boxShadow", "backgroundColor", "light", "contrastText", "align", "colSpan", "py", "mt", "map", "hover", "cursor", "action", "bgcolor", "width", "height", "mr", "fontSize", "fullName", "char<PERSON>t", "toUpperCase", "companyName", "text", "secondary", "phoneNumber", "email", "address", "size", "stopPropagation", "id", "borderTop", "p"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/customer/CustomerDialog.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>alog,\n  DialogTitle,\n  DialogContent,\n  <PERSON>alogActions,\n  Button,\n  TextField,\n  Box,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Typography,\n  InputAdornment,\n  Avatar,\n  useTheme,\n} from '@mui/material';\nimport SearchIcon from '@mui/icons-material/Search';\nimport AddIcon from '@mui/icons-material/Add';\nimport PhoneIcon from '@mui/icons-material/Phone';\nimport EmailIcon from '@mui/icons-material/Email';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\nimport { Customer } from '../../models';\nimport { customerService } from '../../services/customer/customerService';\nimport { LoadingSpinner, ErrorAlert } from '../common';\nimport CustomerForm from './CustomerForm';\nimport { parseDate } from '../../utils/dateUtils';\n\ninterface CustomerDialogProps {\n  open: boolean;\n  onClose: () => void;\n  onSelectCustomer: (customer: Customer) => void;\n}\n\nfunction CustomerDialog({ open, onClose, onSelectCustomer }: CustomerDialogProps) {\n  const [customers, setCustomers] = useState<Customer[]>([]);\n  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showAddCustomerForm, setShowAddCustomerForm] = useState(false);\n\n  const theme = useTheme();\n\n  useEffect(() => {\n    const fetchCustomers = async () => {\n      try {\n        const data = await customerService.getAllCustomers();\n        // Sắp xếp theo thời gian tạo mới nhất\n        const sortedData = [...data].sort((a, b) => {\n          const dateA = parseDate(a.createdAt);\n          const dateB = parseDate(b.createdAt);\n          return dateB.getTime() - dateA.getTime();\n        });\n        setCustomers(sortedData);\n        setFilteredCustomers(sortedData);\n      } catch (err: any) {\n        setError(err.message || 'Đã xảy ra lỗi khi tải danh sách khách hàng');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (open) {\n      fetchCustomers();\n    }\n  }, [open]);\n\n  const handleSearch = async () => {\n    if (!searchTerm.trim()) {\n      setFilteredCustomers(customers);\n      return;\n    }\n\n    try {\n      setLoading(true);\n      // Tìm kiếm theo cả tên và số điện thoại\n      const results = await customerService.searchCustomers(searchTerm, searchTerm);\n      setFilteredCustomers(results);\n\n      if (results.length === 0) {\n        setError('Không tìm thấy khách hàng nào phù hợp');\n      } else {\n        setError(null);\n      }\n    } catch (err: any) {\n      console.error('Error searching customers:', err);\n      setError(err.message || 'Đã xảy ra lỗi khi tìm kiếm khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setSearchTerm(e.target.value);\n    if (!e.target.value.trim()) {\n      setFilteredCustomers(customers);\n    }\n  };\n\n  // Xử lý phím Enter đã được chuyển sang onKeyDown trực tiếp\n\n  const handleAddCustomer = () => {\n    setShowAddCustomerForm(true);\n  };\n\n  // Xử lý sau khi thêm khách hàng mới\n  const handleCustomerAdded = async (_newCustomer: Customer) => {\n    setShowAddCustomerForm(false);\n    setLoading(true);\n    try {\n      const data = await customerService.getAllCustomers();\n      // Sắp xếp theo thời gian tạo mới nhất\n      const sortedData = [...data].sort((a, b) => {\n        const dateA = parseDate(a.createdAt);\n        const dateB = parseDate(b.createdAt);\n        return dateB.getTime() - dateA.getTime();\n      });\n      setCustomers(sortedData);\n      setFilteredCustomers(sortedData);\n    } catch (err: any) {\n      setError(err.message || 'Đã xảy ra lỗi khi tải lại danh sách khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSelectCustomer = (customer: Customer) => {\n    onSelectCustomer(customer);\n    onClose();\n  };\n\n  if (showAddCustomerForm) {\n    return (\n      <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n        <DialogTitle>Thêm khách hàng mới</DialogTitle>\n        <DialogContent>\n          <CustomerForm\n            onSave={handleCustomerAdded}\n            onCancel={() => setShowAddCustomerForm(false)}\n          />\n        </DialogContent>\n      </Dialog>\n    );\n  }\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle sx={{\n        borderBottom: `1px solid ${theme.palette.divider}`,\n        pb: 2\n      }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>\n            Chọn khách hàng\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<AddIcon />}\n            onClick={handleAddCustomer}\n            sx={{\n              borderRadius: '20px',\n              px: 2\n            }}\n          >\n            Thêm khách hàng\n          </Button>\n        </Box>\n      </DialogTitle>\n      <DialogContent sx={{ pt: 3 }}>\n        {error && <ErrorAlert message={error} />}\n\n        <Box sx={{ mb: 3, display: 'flex', gap: 1 }}>\n          <TextField\n            fullWidth\n            placeholder=\"Tìm kiếm theo tên hoặc số điện thoại\"\n            value={searchTerm}\n            onChange={handleSearchChange}\n            onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Enter' && handleSearch()}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon color=\"action\" />\n                </InputAdornment>\n              ),\n            }}\n            sx={{\n              '& .MuiOutlinedInput-root': {\n                borderRadius: '20px',\n              }\n            }}\n          />\n          <Button\n            variant=\"outlined\"\n            onClick={handleSearch}\n            sx={{\n              borderRadius: '20px',\n              px: 3\n            }}\n          >\n            Tìm kiếm\n          </Button>\n        </Box>\n\n        {loading ? (\n          <LoadingSpinner />\n        ) : (\n          <Box>\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Hiển thị {filteredCustomers.length} khách hàng (sắp xếp theo thời gian tạo mới nhất)\n              </Typography>\n            </Box>\n\n            <TableContainer\n              component={Paper}\n              sx={{\n                borderRadius: '8px',\n                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',\n                '& .MuiTableCell-head': {\n                  backgroundColor: theme.palette.primary.light,\n                  color: theme.palette.primary.contrastText,\n                  fontWeight: 'bold',\n                }\n              }}\n            >\n              <Table>\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Tên khách hàng</TableCell>\n                    <TableCell>Công ty</TableCell>\n                    <TableCell>Số điện thoại</TableCell>\n                    <TableCell>Email</TableCell>\n                    <TableCell>Địa chỉ</TableCell>\n                    <TableCell align=\"center\">Thao tác</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {filteredCustomers.length === 0 ? (\n                    <TableRow>\n                      <TableCell colSpan={6} align=\"center\">\n                        <Box sx={{ py: 3 }}>\n                          <Typography variant=\"body1\">Không tìm thấy khách hàng nào</Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                            Thử tìm kiếm với từ khóa khác hoặc thêm khách hàng mới\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  ) : (\n                    filteredCustomers.map((customer) => (\n                      <TableRow\n                        key={customer.id}\n                        hover\n                        sx={{\n                          cursor: 'pointer',\n                          '&:hover': {\n                            backgroundColor: theme.palette.action.hover,\n                          }\n                        }}\n                        onClick={() => handleSelectCustomer(customer)}\n                      >\n                        <TableCell>\n                          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                            <Avatar\n                              sx={{\n                                bgcolor: theme.palette.primary.main,\n                                width: 32,\n                                height: 32,\n                                mr: 1,\n                                fontSize: '0.9rem'\n                              }}\n                            >\n                              {customer.fullName.charAt(0).toUpperCase()}\n                            </Avatar>\n                            <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n                              {customer.fullName}\n                            </Typography>\n                          </Box>\n                        </TableCell>\n                        <TableCell>{customer.companyName || '-'}</TableCell>\n                        <TableCell>\n                          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                            <PhoneIcon fontSize=\"small\" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />\n                            {customer.phoneNumber}\n                          </Box>\n                        </TableCell>\n                        <TableCell>\n                          {customer.email ? (\n                            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                              <EmailIcon fontSize=\"small\" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />\n                              {customer.email}\n                            </Box>\n                          ) : '-'}\n                        </TableCell>\n                        <TableCell>\n                          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                            <LocationOnIcon fontSize=\"small\" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />\n                            {customer.address}\n                          </Box>\n                        </TableCell>\n                        <TableCell align=\"center\">\n                          <Button\n                            variant=\"contained\"\n                            size=\"small\"\n                            onClick={(e: React.MouseEvent<HTMLButtonElement>) => {\n                              e.stopPropagation();\n                              handleSelectCustomer(customer);\n                            }}\n                            sx={{\n                              borderRadius: '20px',\n                              px: 2\n                            }}\n                          >\n                            Chọn\n                          </Button>\n                        </TableCell>\n                      </TableRow>\n                    ))\n                  )}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Box>\n        )}\n      </DialogContent>\n      <DialogActions sx={{ borderTop: `1px solid ${theme.palette.divider}`, p: 2 }}>\n        <Button\n          onClick={onClose}\n          color=\"primary\"\n          variant=\"outlined\"\n          sx={{\n            borderRadius: '20px',\n            px: 3\n          }}\n        >\n          Đóng\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default CustomerDialog;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,MAAM,CACNC,SAAS,CACTC,GAAG,CACHC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,KAAK,CACLC,UAAU,CACVC,cAAc,CACdC,MAAM,CACNC,QAAQ,KACH,eAAe,CACtB,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAE3D,OAASC,eAAe,KAAQ,yCAAyC,CACzE,OAASC,cAAc,CAAEC,UAAU,KAAQ,WAAW,CACtD,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,OAASC,SAAS,KAAQ,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQlD,QAAS,CAAAC,cAAcA,CAAAC,IAAA,CAA2D,IAA1D,CAAEC,IAAI,CAAEC,OAAO,CAAEC,gBAAsC,CAAC,CAAAH,IAAA,CAC9E,KAAM,CAACI,SAAS,CAAEC,YAAY,CAAC,CAAGxC,QAAQ,CAAa,EAAE,CAAC,CAC1D,KAAM,CAACyC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1C,QAAQ,CAAa,EAAE,CAAC,CAC1E,KAAM,CAAC2C,OAAO,CAAEC,UAAU,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6C,KAAK,CAAEC,QAAQ,CAAC,CAAG9C,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAC+C,UAAU,CAAEC,aAAa,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiD,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAErE,KAAM,CAAAmD,KAAK,CAAGhC,QAAQ,CAAC,CAAC,CAExBlB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmD,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAA5B,eAAe,CAAC6B,eAAe,CAAC,CAAC,CACpD;AACA,KAAM,CAAAC,UAAU,CAAG,CAAC,GAAGF,IAAI,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC1C,KAAM,CAAAC,KAAK,CAAG9B,SAAS,CAAC4B,CAAC,CAACG,SAAS,CAAC,CACpC,KAAM,CAAAC,KAAK,CAAGhC,SAAS,CAAC6B,CAAC,CAACE,SAAS,CAAC,CACpC,MAAO,CAAAC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,CAC1C,CAAC,CAAC,CACFtB,YAAY,CAACe,UAAU,CAAC,CACxBb,oBAAoB,CAACa,UAAU,CAAC,CAClC,CAAE,MAAOQ,GAAQ,CAAE,CACjBjB,QAAQ,CAACiB,GAAG,CAACC,OAAO,EAAI,4CAA4C,CAAC,CACvE,CAAC,OAAS,CACRpB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIR,IAAI,CAAE,CACRgB,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAAChB,IAAI,CAAC,CAAC,CAEV,KAAM,CAAA6B,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAAClB,UAAU,CAACmB,IAAI,CAAC,CAAC,CAAE,CACtBxB,oBAAoB,CAACH,SAAS,CAAC,CAC/B,OACF,CAEA,GAAI,CACFK,UAAU,CAAC,IAAI,CAAC,CAChB;AACA,KAAM,CAAAuB,OAAO,CAAG,KAAM,CAAA1C,eAAe,CAAC2C,eAAe,CAACrB,UAAU,CAAEA,UAAU,CAAC,CAC7EL,oBAAoB,CAACyB,OAAO,CAAC,CAE7B,GAAIA,OAAO,CAACE,MAAM,GAAK,CAAC,CAAE,CACxBvB,QAAQ,CAAC,uCAAuC,CAAC,CACnD,CAAC,IAAM,CACLA,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAE,MAAOiB,GAAQ,CAAE,CACjBO,OAAO,CAACzB,KAAK,CAAC,4BAA4B,CAAEkB,GAAG,CAAC,CAChDjB,QAAQ,CAACiB,GAAG,CAACC,OAAO,EAAI,uCAAuC,CAAC,CAClE,CAAC,OAAS,CACRpB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA2B,kBAAkB,CAAIC,CAAsC,EAAK,CACrExB,aAAa,CAACwB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC7B,GAAI,CAACF,CAAC,CAACC,MAAM,CAACC,KAAK,CAACR,IAAI,CAAC,CAAC,CAAE,CAC1BxB,oBAAoB,CAACH,SAAS,CAAC,CACjC,CACF,CAAC,CAED;AAEA,KAAM,CAAAoC,iBAAiB,CAAGA,CAAA,GAAM,CAC9BzB,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAA0B,mBAAmB,CAAG,KAAO,CAAAC,YAAsB,EAAK,CAC5D3B,sBAAsB,CAAC,KAAK,CAAC,CAC7BN,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAS,IAAI,CAAG,KAAM,CAAA5B,eAAe,CAAC6B,eAAe,CAAC,CAAC,CACpD;AACA,KAAM,CAAAC,UAAU,CAAG,CAAC,GAAGF,IAAI,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC1C,KAAM,CAAAC,KAAK,CAAG9B,SAAS,CAAC4B,CAAC,CAACG,SAAS,CAAC,CACpC,KAAM,CAAAC,KAAK,CAAGhC,SAAS,CAAC6B,CAAC,CAACE,SAAS,CAAC,CACpC,MAAO,CAAAC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,CAC1C,CAAC,CAAC,CACFtB,YAAY,CAACe,UAAU,CAAC,CACxBb,oBAAoB,CAACa,UAAU,CAAC,CAClC,CAAE,MAAOQ,GAAQ,CAAE,CACjBjB,QAAQ,CAACiB,GAAG,CAACC,OAAO,EAAI,gDAAgD,CAAC,CAC3E,CAAC,OAAS,CACRpB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAkC,oBAAoB,CAAIC,QAAkB,EAAK,CACnDzC,gBAAgB,CAACyC,QAAQ,CAAC,CAC1B1C,OAAO,CAAC,CAAC,CACX,CAAC,CAED,GAAIY,mBAAmB,CAAE,CACvB,mBACEhB,KAAA,CAAC/B,MAAM,EAACkC,IAAI,CAAEA,IAAK,CAACC,OAAO,CAAEA,OAAQ,CAAC2C,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,eAC3DnD,IAAA,CAAC5B,WAAW,EAAA+E,QAAA,CAAC,mCAAmB,CAAa,CAAC,cAC9CnD,IAAA,CAAC3B,aAAa,EAAA8E,QAAA,cACZnD,IAAA,CAACH,YAAY,EACXuD,MAAM,CAAEP,mBAAoB,CAC5BQ,QAAQ,CAAEA,CAAA,GAAMlC,sBAAsB,CAAC,KAAK,CAAE,CAC/C,CAAC,CACW,CAAC,EACV,CAAC,CAEb,CAEA,mBACEjB,KAAA,CAAC/B,MAAM,EAACkC,IAAI,CAAEA,IAAK,CAACC,OAAO,CAAEA,OAAQ,CAAC2C,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,eAC3DnD,IAAA,CAAC5B,WAAW,EAACkF,EAAE,CAAE,CACfC,YAAY,cAAAC,MAAA,CAAepC,KAAK,CAACqC,OAAO,CAACC,OAAO,CAAE,CAClDC,EAAE,CAAE,CACN,CAAE,CAAAR,QAAA,cACAjD,KAAA,CAACzB,GAAG,EAAC6E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eAClFnD,IAAA,CAACf,UAAU,EAAC8E,OAAO,CAAC,IAAI,CAACT,EAAE,CAAE,CAAEU,UAAU,CAAE,MAAM,CAAEC,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAACS,OAAO,CAACC,IAAK,CAAE,CAAAhB,QAAA,CAAC,4BAExF,CAAY,CAAC,cACbnD,IAAA,CAACzB,MAAM,EACLwF,OAAO,CAAC,WAAW,CACnBE,KAAK,CAAC,SAAS,CACfG,SAAS,cAAEpE,IAAA,CAACV,OAAO,GAAE,CAAE,CACvB+E,OAAO,CAAEzB,iBAAkB,CAC3BU,EAAE,CAAE,CACFgB,YAAY,CAAE,MAAM,CACpBC,EAAE,CAAE,CACN,CAAE,CAAApB,QAAA,CACH,0BAED,CAAQ,CAAC,EACN,CAAC,CACK,CAAC,cACdjD,KAAA,CAAC7B,aAAa,EAACiF,EAAE,CAAE,CAAEkB,EAAE,CAAE,CAAE,CAAE,CAAArB,QAAA,EAC1BrC,KAAK,eAAId,IAAA,CAACJ,UAAU,EAACqC,OAAO,CAAEnB,KAAM,CAAE,CAAC,cAExCZ,KAAA,CAACzB,GAAG,EAAC6E,EAAE,CAAE,CAAEmB,EAAE,CAAE,CAAC,CAAEb,OAAO,CAAE,MAAM,CAAEc,GAAG,CAAE,CAAE,CAAE,CAAAvB,QAAA,eAC1CnD,IAAA,CAACxB,SAAS,EACR0E,SAAS,MACTyB,WAAW,CAAC,0EAAsC,CAClDhC,KAAK,CAAE3B,UAAW,CAClB4D,QAAQ,CAAEpC,kBAAmB,CAC7BqC,SAAS,CAAGpC,CAAwC,EAAKA,CAAC,CAACqC,GAAG,GAAK,OAAO,EAAI5C,YAAY,CAAC,CAAE,CAC7F6C,UAAU,CAAE,CACVC,cAAc,cACZhF,IAAA,CAACd,cAAc,EAAC+F,QAAQ,CAAC,OAAO,CAAA9B,QAAA,cAC9BnD,IAAA,CAACX,UAAU,EAAC4E,KAAK,CAAC,QAAQ,CAAE,CAAC,CACf,CAEpB,CAAE,CACFX,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BgB,YAAY,CAAE,MAChB,CACF,CAAE,CACH,CAAC,cACFtE,IAAA,CAACzB,MAAM,EACLwF,OAAO,CAAC,UAAU,CAClBM,OAAO,CAAEnC,YAAa,CACtBoB,EAAE,CAAE,CACFgB,YAAY,CAAE,MAAM,CACpBC,EAAE,CAAE,CACN,CAAE,CAAApB,QAAA,CACH,kBAED,CAAQ,CAAC,EACN,CAAC,CAELvC,OAAO,cACNZ,IAAA,CAACL,cAAc,GAAE,CAAC,cAElBO,KAAA,CAACzB,GAAG,EAAA0E,QAAA,eACFnD,IAAA,CAACvB,GAAG,EAAC6E,EAAE,CAAE,CAAEmB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,cACjBjD,KAAA,CAACjB,UAAU,EAAC8E,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAd,QAAA,EAAC,qBACxC,CAACzC,iBAAiB,CAAC4B,MAAM,CAAC,uFACrC,EAAY,CAAC,CACV,CAAC,cAENtC,IAAA,CAACnB,cAAc,EACbqG,SAAS,CAAElG,KAAM,CACjBsE,EAAE,CAAE,CACFgB,YAAY,CAAE,KAAK,CACnBa,SAAS,CAAE,4BAA4B,CACvC,sBAAsB,CAAE,CACtBC,eAAe,CAAEhE,KAAK,CAACqC,OAAO,CAACS,OAAO,CAACmB,KAAK,CAC5CpB,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAACS,OAAO,CAACoB,YAAY,CACzCtB,UAAU,CAAE,MACd,CACF,CAAE,CAAAb,QAAA,cAEFjD,KAAA,CAACxB,KAAK,EAAAyE,QAAA,eACJnD,IAAA,CAAClB,SAAS,EAAAqE,QAAA,cACRjD,KAAA,CAACnB,QAAQ,EAAAoE,QAAA,eACPnD,IAAA,CAACpB,SAAS,EAAAuE,QAAA,CAAC,yBAAc,CAAW,CAAC,cACrCnD,IAAA,CAACpB,SAAS,EAAAuE,QAAA,CAAC,YAAO,CAAW,CAAC,cAC9BnD,IAAA,CAACpB,SAAS,EAAAuE,QAAA,CAAC,mCAAa,CAAW,CAAC,cACpCnD,IAAA,CAACpB,SAAS,EAAAuE,QAAA,CAAC,OAAK,CAAW,CAAC,cAC5BnD,IAAA,CAACpB,SAAS,EAAAuE,QAAA,CAAC,wBAAO,CAAW,CAAC,cAC9BnD,IAAA,CAACpB,SAAS,EAAC2G,KAAK,CAAC,QAAQ,CAAApC,QAAA,CAAC,aAAQ,CAAW,CAAC,EACtC,CAAC,CACF,CAAC,cACZnD,IAAA,CAACrB,SAAS,EAAAwE,QAAA,CACPzC,iBAAiB,CAAC4B,MAAM,GAAK,CAAC,cAC7BtC,IAAA,CAACjB,QAAQ,EAAAoE,QAAA,cACPnD,IAAA,CAACpB,SAAS,EAAC4G,OAAO,CAAE,CAAE,CAACD,KAAK,CAAC,QAAQ,CAAApC,QAAA,cACnCjD,KAAA,CAACzB,GAAG,EAAC6E,EAAE,CAAE,CAAEmC,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,eACjBnD,IAAA,CAACf,UAAU,EAAC8E,OAAO,CAAC,OAAO,CAAAZ,QAAA,CAAC,mDAA6B,CAAY,CAAC,cACtEnD,IAAA,CAACf,UAAU,EAAC8E,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAACX,EAAE,CAAE,CAAEoC,EAAE,CAAE,CAAE,CAAE,CAAAvC,QAAA,CAAC,wGAElE,CAAY,CAAC,EACV,CAAC,CACG,CAAC,CACJ,CAAC,CAEXzC,iBAAiB,CAACiF,GAAG,CAAE3C,QAAQ,eAC7B9C,KAAA,CAACnB,QAAQ,EAEP6G,KAAK,MACLtC,EAAE,CAAE,CACFuC,MAAM,CAAE,SAAS,CACjB,SAAS,CAAE,CACTT,eAAe,CAAEhE,KAAK,CAACqC,OAAO,CAACqC,MAAM,CAACF,KACxC,CACF,CAAE,CACFvB,OAAO,CAAEA,CAAA,GAAMtB,oBAAoB,CAACC,QAAQ,CAAE,CAAAG,QAAA,eAE9CnD,IAAA,CAACpB,SAAS,EAAAuE,QAAA,cACRjD,KAAA,CAACzB,GAAG,EAAC6E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eACjDnD,IAAA,CAACb,MAAM,EACLmE,EAAE,CAAE,CACFyC,OAAO,CAAE3E,KAAK,CAACqC,OAAO,CAACS,OAAO,CAACC,IAAI,CACnC6B,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,EAAE,CAAE,CAAC,CACLC,QAAQ,CAAE,QACZ,CAAE,CAAAhD,QAAA,CAEDH,QAAQ,CAACoD,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACpC,CAAC,cACTtG,IAAA,CAACf,UAAU,EAAC8E,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEU,UAAU,CAAE,QAAS,CAAE,CAAAb,QAAA,CACtDH,QAAQ,CAACoD,QAAQ,CACR,CAAC,EACV,CAAC,CACG,CAAC,cACZpG,IAAA,CAACpB,SAAS,EAAAuE,QAAA,CAAEH,QAAQ,CAACuD,WAAW,EAAI,GAAG,CAAY,CAAC,cACpDvG,IAAA,CAACpB,SAAS,EAAAuE,QAAA,cACRjD,KAAA,CAACzB,GAAG,EAAC6E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eACjDnD,IAAA,CAACT,SAAS,EAAC4G,QAAQ,CAAC,OAAO,CAAC7C,EAAE,CAAE,CAAE4C,EAAE,CAAE,GAAG,CAAEjC,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAAC+C,IAAI,CAACC,SAAU,CAAE,CAAE,CAAC,CACnFzD,QAAQ,CAAC0D,WAAW,EAClB,CAAC,CACG,CAAC,cACZ1G,IAAA,CAACpB,SAAS,EAAAuE,QAAA,CACPH,QAAQ,CAAC2D,KAAK,cACbzG,KAAA,CAACzB,GAAG,EAAC6E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eACjDnD,IAAA,CAACR,SAAS,EAAC2G,QAAQ,CAAC,OAAO,CAAC7C,EAAE,CAAE,CAAE4C,EAAE,CAAE,GAAG,CAAEjC,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAAC+C,IAAI,CAACC,SAAU,CAAE,CAAE,CAAC,CACnFzD,QAAQ,CAAC2D,KAAK,EACZ,CAAC,CACJ,GAAG,CACE,CAAC,cACZ3G,IAAA,CAACpB,SAAS,EAAAuE,QAAA,cACRjD,KAAA,CAACzB,GAAG,EAAC6E,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAX,QAAA,eACjDnD,IAAA,CAACP,cAAc,EAAC0G,QAAQ,CAAC,OAAO,CAAC7C,EAAE,CAAE,CAAE4C,EAAE,CAAE,GAAG,CAAEjC,KAAK,CAAE7C,KAAK,CAACqC,OAAO,CAAC+C,IAAI,CAACC,SAAU,CAAE,CAAE,CAAC,CACxFzD,QAAQ,CAAC4D,OAAO,EACd,CAAC,CACG,CAAC,cACZ5G,IAAA,CAACpB,SAAS,EAAC2G,KAAK,CAAC,QAAQ,CAAApC,QAAA,cACvBnD,IAAA,CAACzB,MAAM,EACLwF,OAAO,CAAC,WAAW,CACnB8C,IAAI,CAAC,OAAO,CACZxC,OAAO,CAAG5B,CAAsC,EAAK,CACnDA,CAAC,CAACqE,eAAe,CAAC,CAAC,CACnB/D,oBAAoB,CAACC,QAAQ,CAAC,CAChC,CAAE,CACFM,EAAE,CAAE,CACFgB,YAAY,CAAE,MAAM,CACpBC,EAAE,CAAE,CACN,CAAE,CAAApB,QAAA,CACH,WAED,CAAQ,CAAC,CACA,CAAC,GAhEPH,QAAQ,CAAC+D,EAiEN,CACX,CACF,CACQ,CAAC,EACP,CAAC,CACM,CAAC,EACd,CACN,EACY,CAAC,cAChB/G,IAAA,CAAC1B,aAAa,EAACgF,EAAE,CAAE,CAAE0D,SAAS,cAAAxD,MAAA,CAAepC,KAAK,CAACqC,OAAO,CAACC,OAAO,CAAE,CAAEuD,CAAC,CAAE,CAAE,CAAE,CAAA9D,QAAA,cAC3EnD,IAAA,CAACzB,MAAM,EACL8F,OAAO,CAAE/D,OAAQ,CACjB2D,KAAK,CAAC,SAAS,CACfF,OAAO,CAAC,UAAU,CAClBT,EAAE,CAAE,CACFgB,YAAY,CAAE,MAAM,CACpBC,EAAE,CAAE,CACN,CAAE,CAAApB,QAAA,CACH,cAED,CAAQ,CAAC,CACI,CAAC,EACV,CAAC,CAEb,CAAC,CAED,cAAe,CAAAhD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}