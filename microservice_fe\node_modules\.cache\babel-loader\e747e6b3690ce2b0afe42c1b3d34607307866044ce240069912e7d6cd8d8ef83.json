{"ast": null, "code": "import React from'react';import{Dialog,DialogTitle,DialogContent,DialogContentText,DialogActions,Button}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ConfirmDialog(_ref){let{open,title,message,onConfirm,onCancel,confirmText='Xác nhận',cancelText='Hủy',severity='warning'}=_ref;const getButtonColor=()=>{switch(severity){case'error':return'error';case'warning':return'warning';case'info':default:return'primary';}};return/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:onCancel,children:[/*#__PURE__*/_jsx(DialogTitle,{children:title}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(DialogContentText,{children:message})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:onCancel,color:\"inherit\",children:cancelText}),/*#__PURE__*/_jsx(Button,{onClick:onConfirm,color:getButtonColor(),variant:\"contained\",autoFocus:true,children:confirmText})]})]});};export default ConfirmDialog;", "map": {"version": 3, "names": ["React", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "ConfirmDialog", "_ref", "open", "title", "message", "onConfirm", "onCancel", "confirmText", "cancelText", "severity", "getButtonColor", "onClose", "children", "onClick", "color", "variant", "autoFocus"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/ConfirmDialog.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  Button,\n} from '@mui/material';\n\ninterface ConfirmDialogProps {\n  open: boolean;\n  title: string;\n  message: string;\n  onConfirm: () => void;\n  onCancel: () => void;\n  confirmText?: string;\n  cancelText?: string;\n  severity?: 'warning' | 'error' | 'info';\n}\n\nfunction ConfirmDialog({\n  open,\n  title,\n  message,\n  onConfirm,\n  onCancel,\n  confirmText = 'Xác nhận',\n  cancelText = 'Hủy',\n  severity = 'warning',\n}: ConfirmDialogProps) {\n  const getButtonColor = () => {\n    switch (severity) {\n      case 'error':\n        return 'error';\n      case 'warning':\n        return 'warning';\n      case 'info':\n      default:\n        return 'primary';\n    }\n  };\n\n  return (\n    <Dialog open={open} onClose={onCancel}>\n      <DialogTitle>{title}</DialogTitle>\n      <DialogContent>\n        <DialogContentText>{message}</DialogContentText>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onCancel} color=\"inherit\">\n          {cancelText}\n        </Button>\n        <Button onClick={onConfirm} color={getButtonColor()} variant=\"contained\" autoFocus>\n          {confirmText}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ConfirmDialog;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,iBAAiB,CACjBC,aAAa,CACbC,MAAM,KACD,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAavB,QAAS,CAAAC,aAAaA,CAAAC,IAAA,CASC,IATA,CACrBC,IAAI,CACJC,KAAK,CACLC,OAAO,CACPC,SAAS,CACTC,QAAQ,CACRC,WAAW,CAAG,UAAU,CACxBC,UAAU,CAAG,KAAK,CAClBC,QAAQ,CAAG,SACO,CAAC,CAAAR,IAAA,CACnB,KAAM,CAAAS,cAAc,CAAGA,CAAA,GAAM,CAC3B,OAAQD,QAAQ,EACd,IAAK,OAAO,CACV,MAAO,OAAO,CAChB,IAAK,SAAS,CACZ,MAAO,SAAS,CAClB,IAAK,MAAM,CACX,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED,mBACEV,KAAA,CAACT,MAAM,EAACY,IAAI,CAAEA,IAAK,CAACS,OAAO,CAAEL,QAAS,CAAAM,QAAA,eACpCf,IAAA,CAACN,WAAW,EAAAqB,QAAA,CAAET,KAAK,CAAc,CAAC,cAClCN,IAAA,CAACL,aAAa,EAAAoB,QAAA,cACZf,IAAA,CAACJ,iBAAiB,EAAAmB,QAAA,CAAER,OAAO,CAAoB,CAAC,CACnC,CAAC,cAChBL,KAAA,CAACL,aAAa,EAAAkB,QAAA,eACZf,IAAA,CAACF,MAAM,EAACkB,OAAO,CAAEP,QAAS,CAACQ,KAAK,CAAC,SAAS,CAAAF,QAAA,CACvCJ,UAAU,CACL,CAAC,cACTX,IAAA,CAACF,MAAM,EAACkB,OAAO,CAAER,SAAU,CAACS,KAAK,CAAEJ,cAAc,CAAC,CAAE,CAACK,OAAO,CAAC,WAAW,CAACC,SAAS,MAAAJ,QAAA,CAC/EL,WAAW,CACN,CAAC,EACI,CAAC,EACV,CAAC,CAEb,CAAC,CAED,cAAe,CAAAP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}