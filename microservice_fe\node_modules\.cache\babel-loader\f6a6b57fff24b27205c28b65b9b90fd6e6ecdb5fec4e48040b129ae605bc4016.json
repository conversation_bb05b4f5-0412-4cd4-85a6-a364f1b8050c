{"ast": null, "code": "import React from'react';import{Card}from'primereact/card';import{Divider}from'primereact/divider';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatisticsSummary=_ref=>{let{data,periodType,periodLabel}=_ref;// Calculate total revenue\nconst totalRevenue=data.reduce((sum,item)=>sum+item.totalRevenue,0);// Calculate total invoices\nconst totalInvoices=data.reduce((sum,item)=>sum+item.invoiceCount,0);// Format currency\nconst formatCurrency=value=>{return new Intl.NumberFormat('vi-VN',{style:'currency',currency:'VND',maximumFractionDigits:0}).format(value);};// Get period type label\nconst getPeriodTypeLabel=()=>{switch(periodType){case'daily':return'ngày';case'monthly':return'tháng';case'quarterly':return'quý';case'yearly':return'năm';default:return'khoảng thời gian';}};// Get summary title\nconst getSummaryTitle=()=>{if(periodLabel){return\"T\\u1ED5ng h\\u1EE3p doanh thu \".concat(getPeriodTypeLabel(),\" \").concat(periodLabel);}return\"T\\u1ED5ng h\\u1EE3p doanh thu theo \".concat(getPeriodTypeLabel());};return/*#__PURE__*/_jsxs(Card,{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card-header\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-2\",children:getSummaryTitle()}),/*#__PURE__*/_jsx(Divider,{})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 border-round bg-primary text-primary-contrast\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm mb-2\",children:\"T\\u1ED5ng doanh thu\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold\",children:formatCurrency(totalRevenue)})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"col-12 md:col-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 border-round bg-blue-500 text-white\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm mb-2\",children:\"T\\u1ED5ng s\\u1ED1 h\\xF3a \\u0111\\u01A1n\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold\",children:totalInvoices})]})})]})})]});};export default StatisticsSummary;", "map": {"version": 3, "names": ["React", "Card", "Divider", "jsx", "_jsx", "jsxs", "_jsxs", "StatisticsSummary", "_ref", "data", "periodType", "period<PERSON><PERSON><PERSON>", "totalRevenue", "reduce", "sum", "item", "totalInvoices", "invoiceCount", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "getPeriodTypeLabel", "getSummaryTitle", "concat", "className", "children"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/StatisticsSummary.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card } from 'primereact/card';\nimport { Divider } from 'primereact/divider';\nimport { TimeBasedRevenue } from '../../models';\n\ninterface StatisticsSummaryProps {\n  data: TimeBasedRevenue[];\n  periodType: string;\n  periodLabel?: string;\n}\n\nconst StatisticsSummary: React.FC<StatisticsSummaryProps> = ({\n  data,\n  periodType,\n  periodLabel\n}) => {\n  // Calculate total revenue\n  const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);\n\n  // Calculate total invoices\n  const totalInvoices = data.reduce((sum, item) => sum + item.invoiceCount, 0);\n\n  // Format currency\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND',\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  // Get period type label\n  const getPeriodTypeLabel = (): string => {\n    switch (periodType) {\n      case 'daily':\n        return 'ngày';\n      case 'monthly':\n        return 'tháng';\n      case 'quarterly':\n        return 'quý';\n      case 'yearly':\n        return 'năm';\n      default:\n        return 'khoảng thời gian';\n    }\n  };\n\n  // Get summary title\n  const getSummaryTitle = (): string => {\n    if (periodLabel) {\n      return `Tổng hợp doanh thu ${getPeriodTypeLabel()} ${periodLabel}`;\n    }\n    return `Tổng hợp doanh thu theo ${getPeriodTypeLabel()}`;\n  };\n\n  return (\n    <Card className=\"mb-3\">\n      <div className=\"card-header\">\n        <h3 className=\"text-xl font-semibold mb-2\">{getSummaryTitle()}</h3>\n        <Divider />\n      </div>\n\n      <div className=\"card-body\">\n        <div className=\"grid\">\n          <div className=\"col-12 md:col-6\">\n            <div className=\"p-3 border-round bg-primary text-primary-contrast\">\n              <div className=\"text-sm mb-2\">Tổng doanh thu</div>\n              <div className=\"text-3xl font-bold\">{formatCurrency(totalRevenue)}</div>\n            </div>\n          </div>\n\n          <div className=\"col-12 md:col-6\">\n            <div className=\"p-3 border-round bg-blue-500 text-white\">\n              <div className=\"text-sm mb-2\">Tổng số hóa đơn</div>\n              <div className=\"text-3xl font-bold\">{totalInvoices}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default StatisticsSummary;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OAASC,OAAO,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAS7C,KAAM,CAAAC,iBAAmD,CAAGC,IAAA,EAItD,IAJuD,CAC3DC,IAAI,CACJC,UAAU,CACVC,WACF,CAAC,CAAAH,IAAA,CACC;AACA,KAAM,CAAAI,YAAY,CAAGH,IAAI,CAACI,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAKD,GAAG,CAAGC,IAAI,CAACH,YAAY,CAAE,CAAC,CAAC,CAE3E;AACA,KAAM,CAAAI,aAAa,CAAGP,IAAI,CAACI,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAKD,GAAG,CAAGC,IAAI,CAACE,YAAY,CAAE,CAAC,CAAC,CAE5E;AACA,KAAM,CAAAC,cAAc,CAAIC,KAAa,EAAa,CAChD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACN,KAAK,CAAC,CAClB,CAAC,CAED;AACA,KAAM,CAAAO,kBAAkB,CAAGA,CAAA,GAAc,CACvC,OAAQhB,UAAU,EAChB,IAAK,OAAO,CACV,MAAO,MAAM,CACf,IAAK,SAAS,CACZ,MAAO,OAAO,CAChB,IAAK,WAAW,CACd,MAAO,KAAK,CACd,IAAK,QAAQ,CACX,MAAO,KAAK,CACd,QACE,MAAO,kBAAkB,CAC7B,CACF,CAAC,CAED;AACA,KAAM,CAAAiB,eAAe,CAAGA,CAAA,GAAc,CACpC,GAAIhB,WAAW,CAAE,CACf,sCAAAiB,MAAA,CAA6BF,kBAAkB,CAAC,CAAC,MAAAE,MAAA,CAAIjB,WAAW,EAClE,CACA,2CAAAiB,MAAA,CAAkCF,kBAAkB,CAAC,CAAC,EACxD,CAAC,CAED,mBACEpB,KAAA,CAACL,IAAI,EAAC4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACpBxB,KAAA,QAAKuB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B1B,IAAA,OAAIyB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEH,eAAe,CAAC,CAAC,CAAK,CAAC,cACnEvB,IAAA,CAACF,OAAO,GAAE,CAAC,EACR,CAAC,cAENE,IAAA,QAAKyB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBxB,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1B,IAAA,QAAKyB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BxB,KAAA,QAAKuB,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChE1B,IAAA,QAAKyB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,qBAAc,CAAK,CAAC,cAClD1B,IAAA,QAAKyB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEZ,cAAc,CAACN,YAAY,CAAC,CAAM,CAAC,EACrE,CAAC,CACH,CAAC,cAENR,IAAA,QAAKyB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BxB,KAAA,QAAKuB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtD1B,IAAA,QAAKyB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,wCAAe,CAAK,CAAC,cACnD1B,IAAA,QAAKyB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEd,aAAa,CAAM,CAAC,EACtD,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,EACF,CAAC,CAEX,CAAC,CAED,cAAe,CAAAT,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}