{"ast": null, "code": "import _objectSpread from\"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Box,TextField,Button}from'@mui/material';import{customerService}from'../../services/customer/customerService';import{LoadingSpinner,ErrorAlert,SuccessAlert}from'../common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CustomerForm(_ref){let{customer,onSave,onCancel}=_ref;const[formData,setFormData]=useState(customer||{fullName:'',companyName:'',phoneNumber:'',email:'',address:''});const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[success,setSuccess]=useState(null);const handleInputChange=e=>{const{name,value}=e.target;setFormData(_objectSpread(_objectSpread({},formData),{},{[name]:value}));};const validateForm=()=>{if(!formData.fullName){setError('Vui lòng nhập tên khách hàng');return false;}if(!formData.phoneNumber){setError('Vui lòng nhập số điện thoại');return false;}// Address is optional for customers\n// if (!formData.address) {\n//   setError('Vui lòng nhập địa chỉ');\n//   return false;\n// }\n// Kiểm tra định dạng email nếu có\nif(formData.email&&!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)){setError('Email không hợp lệ');return false;}// Kiểm tra định dạng số điện thoại\nif(!/^\\d{10}$/.test(formData.phoneNumber)){setError('Số điện thoại phải có 10 chữ số');return false;}return true;};const handleSubmit=async e=>{e.preventDefault();// Prevent double submission\nif(loading){return;}setError(null);if(!validateForm()){return;}setLoading(true);try{let savedCustomer;if(customer!==null&&customer!==void 0&&customer.id){// Cập nhật khách hàng hiện có\nsavedCustomer=await customerService.updateCustomer(formData);setSuccess('Cập nhật khách hàng thành công!');}else{// Tạo khách hàng mới\nsavedCustomer=await customerService.createCustomer(formData);setSuccess('Thêm khách hàng thành công!');}// Chờ một chút để hiển thị thông báo thành công\nsetTimeout(()=>{onSave(savedCustomer);},1000);}catch(err){setError(err.message||'Đã xảy ra lỗi khi lưu thông tin khách hàng');}finally{setLoading(false);}};return/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleSubmit,sx:{mt:2},children:[error&&/*#__PURE__*/_jsx(ErrorAlert,{message:error}),success&&/*#__PURE__*/_jsx(SuccessAlert,{message:success}),loading&&/*#__PURE__*/_jsx(LoadingSpinner,{}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:2},children:[/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'calc(50% - 8px)'}},children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"T\\xEAn kh\\xE1ch h\\xE0ng\",name:\"fullName\",value:formData.fullName||'',onChange:handleInputChange,required:true})}),/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'calc(50% - 8px)'}},children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"T\\xEAn c\\xF4ng ty\",name:\"companyName\",value:formData.companyName||'',onChange:handleInputChange})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:2},children:[/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'calc(50% - 8px)'}},children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\",name:\"phoneNumber\",value:formData.phoneNumber||'',onChange:handleInputChange,required:true,sx:{'& input':{maxLength:10}},helperText:\"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i ph\\u1EA3i c\\xF3 10 ch\\u1EEF s\\u1ED1\"})}),/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'calc(50% - 8px)'}},children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"Email\",name:\"email\",type:\"email\",value:formData.email||'',onChange:handleInputChange})})]}),/*#__PURE__*/_jsx(Box,{sx:{width:'100%'},children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"\\u0110\\u1ECBa ch\\u1EC9\",name:\"address\",value:formData.address||'',onChange:handleInputChange,multiline:true,rows:2,placeholder:\"\\u0110\\u1ECBa ch\\u1EC9 kh\\xE1ch h\\xE0ng (kh\\xF4ng b\\u1EAFt bu\\u1ED9c)\"})})]}),/*#__PURE__*/_jsxs(Box,{sx:{mt:3,display:'flex',justifyContent:'flex-end',gap:2},children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"primary\",onClick:onCancel,disabled:loading,children:\"H\\u1EE7y\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",color:\"primary\",disabled:loading,children:loading?'Đang xử lý...':customer!==null&&customer!==void 0&&customer.id?'Cập nhật':'Thêm mới'})]})]});};export default CustomerForm;", "map": {"version": 3, "names": ["React", "useState", "Box", "TextField", "<PERSON><PERSON>", "customerService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "CustomerForm", "_ref", "customer", "onSave", "onCancel", "formData", "setFormData", "fullName", "companyName", "phoneNumber", "email", "address", "loading", "setLoading", "error", "setError", "success", "setSuccess", "handleInputChange", "e", "name", "value", "target", "_objectSpread", "validateForm", "test", "handleSubmit", "preventDefault", "savedCustomer", "id", "updateCustomer", "createCustomer", "setTimeout", "err", "message", "component", "onSubmit", "sx", "mt", "children", "display", "flexDirection", "gap", "flexWrap", "width", "xs", "md", "fullWidth", "label", "onChange", "required", "max<PERSON><PERSON><PERSON>", "helperText", "type", "multiline", "rows", "placeholder", "justifyContent", "variant", "color", "onClick", "disabled"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/customer/CustomerForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n} from '@mui/material';\nimport { Customer } from '../../models';\nimport { customerService } from '../../services/customer/customerService';\nimport { Loading<PERSON>pinner, <PERSON>rror<PERSON><PERSON>t, SuccessAlert } from '../common';\n\ninterface CustomerFormProps {\n  customer?: Customer;\n  onSave: (customer: Customer) => void;\n  onCancel: () => void;\n}\n\nfunction CustomerForm({ customer, onSave, onCancel }: CustomerFormProps) {\n  const [formData, setFormData] = useState<Partial<Customer>>(\n    customer || {\n      fullName: '',\n      companyName: '',\n      phoneNumber: '',\n      email: '',\n      address: '',\n    }\n  );\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value,\n    });\n  };\n\n  const validateForm = (): boolean => {\n    if (!formData.fullName) {\n      setError('Vui lòng nhập tên khách hàng');\n      return false;\n    }\n\n    if (!formData.phoneNumber) {\n      setError('Vui lòng nhập số điện thoại');\n      return false;\n    }\n\n    // Address is optional for customers\n    // if (!formData.address) {\n    //   setError('Vui lòng nhập địa chỉ');\n    //   return false;\n    // }\n\n    // Kiểm tra định dạng email nếu có\n    if (formData.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      setError('Email không hợp lệ');\n      return false;\n    }\n\n    // Kiểm tra định dạng số điện thoại\n    if (!/^\\d{10}$/.test(formData.phoneNumber)) {\n      setError('Số điện thoại phải có 10 chữ số');\n      return false;\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Prevent double submission\n    if (loading) {\n      return;\n    }\n\n    setError(null);\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      let savedCustomer: Customer;\n\n      if (customer?.id) {\n        // Cập nhật khách hàng hiện có\n        savedCustomer = await customerService.updateCustomer(formData as Customer);\n        setSuccess('Cập nhật khách hàng thành công!');\n      } else {\n        // Tạo khách hàng mới\n        savedCustomer = await customerService.createCustomer(formData as Customer);\n        setSuccess('Thêm khách hàng thành công!');\n      }\n\n      // Chờ một chút để hiển thị thông báo thành công\n      setTimeout(() => {\n        onSave(savedCustomer);\n      }, 1000);\n    } catch (err: any) {\n      setError(err.message || 'Đã xảy ra lỗi khi lưu thông tin khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} sx={{ mt: 2 }}>\n      {error && <ErrorAlert message={error} />}\n      {success && <SuccessAlert message={success} />}\n      {loading && <LoadingSpinner />}\n\n      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 8px)' } }}>\n            <TextField\n              fullWidth\n              label=\"Tên khách hàng\"\n              name=\"fullName\"\n              value={formData.fullName || ''}\n              onChange={handleInputChange}\n              required\n            />\n          </Box>\n          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 8px)' } }}>\n            <TextField\n              fullWidth\n              label=\"Tên công ty\"\n              name=\"companyName\"\n              value={formData.companyName || ''}\n              onChange={handleInputChange}\n            />\n          </Box>\n        </Box>\n\n        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 8px)' } }}>\n            <TextField\n              fullWidth\n              label=\"Số điện thoại\"\n              name=\"phoneNumber\"\n              value={formData.phoneNumber || ''}\n              onChange={handleInputChange}\n              required\n              sx={{ '& input': { maxLength: 10 } }}\n              helperText=\"Số điện thoại phải có 10 chữ số\"\n            />\n          </Box>\n          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 8px)' } }}>\n            <TextField\n              fullWidth\n              label=\"Email\"\n              name=\"email\"\n              type=\"email\"\n              value={formData.email || ''}\n              onChange={handleInputChange}\n            />\n          </Box>\n        </Box>\n\n        <Box sx={{ width: '100%' }}>\n          <TextField\n            fullWidth\n            label=\"Địa chỉ\"\n            name=\"address\"\n            value={formData.address || ''}\n            onChange={handleInputChange}\n            multiline\n            rows={2}\n            placeholder=\"Địa chỉ khách hàng (không bắt buộc)\"\n          />\n        </Box>\n      </Box>\n\n      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n        <Button\n          variant=\"outlined\"\n          color=\"primary\"\n          onClick={onCancel}\n          disabled={loading}\n        >\n          Hủy\n        </Button>\n        <Button\n          type=\"submit\"\n          variant=\"contained\"\n          color=\"primary\"\n          disabled={loading}\n        >\n          {loading ? 'Đang xử lý...' : (customer?.id ? 'Cập nhật' : 'Thêm mới')}\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default CustomerForm;\n"], "mappings": "gKAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,SAAS,CACTC,MAAM,KACD,eAAe,CAEtB,OAASC,eAAe,KAAQ,yCAAyC,CACzE,OAASC,cAAc,CAAEC,UAAU,CAAEC,YAAY,KAAQ,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQrE,QAAS,CAAAC,YAAYA,CAAAC,IAAA,CAAoD,IAAnD,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,QAA4B,CAAC,CAAAH,IAAA,CACrE,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAGlB,QAAQ,CACtCc,QAAQ,EAAI,CACVK,QAAQ,CAAE,EAAE,CACZC,WAAW,CAAE,EAAE,CACfC,WAAW,CAAE,EAAE,CACfC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EACX,CACF,CAAC,CACD,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC0B,KAAK,CAAEC,QAAQ,CAAC,CAAG3B,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAgB,IAAI,CAAC,CAE3D,KAAM,CAAA8B,iBAAiB,CAAIC,CAAsC,EAAK,CACpE,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChChB,WAAW,CAAAiB,aAAA,CAAAA,aAAA,IACNlB,QAAQ,MACX,CAACe,IAAI,EAAGC,KAAK,EACd,CAAC,CACJ,CAAC,CAED,KAAM,CAAAG,YAAY,CAAGA,CAAA,GAAe,CAClC,GAAI,CAACnB,QAAQ,CAACE,QAAQ,CAAE,CACtBQ,QAAQ,CAAC,8BAA8B,CAAC,CACxC,MAAO,MAAK,CACd,CAEA,GAAI,CAACV,QAAQ,CAACI,WAAW,CAAE,CACzBM,QAAQ,CAAC,6BAA6B,CAAC,CACvC,MAAO,MAAK,CACd,CAEA;AACA;AACA;AACA;AACA;AAEA;AACA,GAAIV,QAAQ,CAACK,KAAK,EAAI,CAAC,4BAA4B,CAACe,IAAI,CAACpB,QAAQ,CAACK,KAAK,CAAC,CAAE,CACxEK,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MAAO,MAAK,CACd,CAEA;AACA,GAAI,CAAC,UAAU,CAACU,IAAI,CAACpB,QAAQ,CAACI,WAAW,CAAC,CAAE,CAC1CM,QAAQ,CAAC,iCAAiC,CAAC,CAC3C,MAAO,MAAK,CACd,CAEA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAW,YAAY,CAAG,KAAO,CAAAP,CAAkB,EAAK,CACjDA,CAAC,CAACQ,cAAc,CAAC,CAAC,CAElB;AACA,GAAIf,OAAO,CAAE,CACX,OACF,CAEAG,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CAACS,YAAY,CAAC,CAAC,CAAE,CACnB,OACF,CAEAX,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,GAAI,CAAAe,aAAuB,CAE3B,GAAI1B,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAE2B,EAAE,CAAE,CAChB;AACAD,aAAa,CAAG,KAAM,CAAApC,eAAe,CAACsC,cAAc,CAACzB,QAAoB,CAAC,CAC1EY,UAAU,CAAC,iCAAiC,CAAC,CAC/C,CAAC,IAAM,CACL;AACAW,aAAa,CAAG,KAAM,CAAApC,eAAe,CAACuC,cAAc,CAAC1B,QAAoB,CAAC,CAC1EY,UAAU,CAAC,6BAA6B,CAAC,CAC3C,CAEA;AACAe,UAAU,CAAC,IAAM,CACf7B,MAAM,CAACyB,aAAa,CAAC,CACvB,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOK,GAAQ,CAAE,CACjBlB,QAAQ,CAACkB,GAAG,CAACC,OAAO,EAAI,4CAA4C,CAAC,CACvE,CAAC,OAAS,CACRrB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEd,KAAA,CAACV,GAAG,EAAC8C,SAAS,CAAC,MAAM,CAACC,QAAQ,CAAEV,YAAa,CAACW,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,EACzDzB,KAAK,eAAIjB,IAAA,CAACH,UAAU,EAACwC,OAAO,CAAEpB,KAAM,CAAE,CAAC,CACvCE,OAAO,eAAInB,IAAA,CAACF,YAAY,EAACuC,OAAO,CAAElB,OAAQ,CAAE,CAAC,CAC7CJ,OAAO,eAAIf,IAAA,CAACJ,cAAc,GAAE,CAAC,cAE9BM,KAAA,CAACV,GAAG,EAACgD,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAH,QAAA,eAC5DxC,KAAA,CAACV,GAAG,EAACgD,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEG,QAAQ,CAAE,MAAM,CAAED,GAAG,CAAE,CAAE,CAAE,CAAAH,QAAA,eACrD1C,IAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CAAEO,KAAK,CAAE,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,iBAAkB,CAAE,CAAE,CAAAP,QAAA,cACxD1C,IAAA,CAACP,SAAS,EACRyD,SAAS,MACTC,KAAK,CAAC,yBAAgB,CACtB5B,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEhB,QAAQ,CAACE,QAAQ,EAAI,EAAG,CAC/B0C,QAAQ,CAAE/B,iBAAkB,CAC5BgC,QAAQ,MACT,CAAC,CACC,CAAC,cACNrD,IAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CAAEO,KAAK,CAAE,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,iBAAkB,CAAE,CAAE,CAAAP,QAAA,cACxD1C,IAAA,CAACP,SAAS,EACRyD,SAAS,MACTC,KAAK,CAAC,mBAAa,CACnB5B,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAEhB,QAAQ,CAACG,WAAW,EAAI,EAAG,CAClCyC,QAAQ,CAAE/B,iBAAkB,CAC7B,CAAC,CACC,CAAC,EACH,CAAC,cAENnB,KAAA,CAACV,GAAG,EAACgD,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEG,QAAQ,CAAE,MAAM,CAAED,GAAG,CAAE,CAAE,CAAE,CAAAH,QAAA,eACrD1C,IAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CAAEO,KAAK,CAAE,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,iBAAkB,CAAE,CAAE,CAAAP,QAAA,cACxD1C,IAAA,CAACP,SAAS,EACRyD,SAAS,MACTC,KAAK,CAAC,mCAAe,CACrB5B,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAEhB,QAAQ,CAACI,WAAW,EAAI,EAAG,CAClCwC,QAAQ,CAAE/B,iBAAkB,CAC5BgC,QAAQ,MACRb,EAAE,CAAE,CAAE,SAAS,CAAE,CAAEc,SAAS,CAAE,EAAG,CAAE,CAAE,CACrCC,UAAU,CAAC,uEAAiC,CAC7C,CAAC,CACC,CAAC,cACNvD,IAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CAAEO,KAAK,CAAE,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,iBAAkB,CAAE,CAAE,CAAAP,QAAA,cACxD1C,IAAA,CAACP,SAAS,EACRyD,SAAS,MACTC,KAAK,CAAC,OAAO,CACb5B,IAAI,CAAC,OAAO,CACZiC,IAAI,CAAC,OAAO,CACZhC,KAAK,CAAEhB,QAAQ,CAACK,KAAK,EAAI,EAAG,CAC5BuC,QAAQ,CAAE/B,iBAAkB,CAC7B,CAAC,CACC,CAAC,EACH,CAAC,cAENrB,IAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CAAEO,KAAK,CAAE,MAAO,CAAE,CAAAL,QAAA,cACzB1C,IAAA,CAACP,SAAS,EACRyD,SAAS,MACTC,KAAK,CAAC,wBAAS,CACf5B,IAAI,CAAC,SAAS,CACdC,KAAK,CAAEhB,QAAQ,CAACM,OAAO,EAAI,EAAG,CAC9BsC,QAAQ,CAAE/B,iBAAkB,CAC5BoC,SAAS,MACTC,IAAI,CAAE,CAAE,CACRC,WAAW,CAAC,uEAAqC,CAClD,CAAC,CACC,CAAC,EACH,CAAC,cAENzD,KAAA,CAACV,GAAG,EAACgD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEE,OAAO,CAAE,MAAM,CAAEiB,cAAc,CAAE,UAAU,CAAEf,GAAG,CAAE,CAAE,CAAE,CAAAH,QAAA,eACtE1C,IAAA,CAACN,MAAM,EACLmE,OAAO,CAAC,UAAU,CAClBC,KAAK,CAAC,SAAS,CACfC,OAAO,CAAExD,QAAS,CAClByD,QAAQ,CAAEjD,OAAQ,CAAA2B,QAAA,CACnB,UAED,CAAQ,CAAC,cACT1C,IAAA,CAACN,MAAM,EACL8D,IAAI,CAAC,QAAQ,CACbK,OAAO,CAAC,WAAW,CACnBC,KAAK,CAAC,SAAS,CACfE,QAAQ,CAAEjD,OAAQ,CAAA2B,QAAA,CAEjB3B,OAAO,CAAG,eAAe,CAAIV,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAE2B,EAAE,CAAG,UAAU,CAAG,UAAW,CAC/D,CAAC,EACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}