{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\statistics\\\\StatisticsSummary.tsx\";\nimport React from 'react';\nimport { Card } from 'primereact/card';\nimport { Divider } from 'primereact/divider';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatisticsSummary = ({\n  data,\n  periodType,\n  periodLabel\n}) => {\n  // Calculate total revenue\n  const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);\n\n  // Calculate total invoices\n  const totalInvoices = data.reduce((sum, item) => sum + item.invoiceCount, 0);\n\n  // Format currency\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND',\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  // Get period type label\n  const getPeriodTypeLabel = () => {\n    switch (periodType) {\n      case 'daily':\n        return 'ngày';\n      case 'monthly':\n        return 'tháng';\n      case 'quarterly':\n        return 'quý';\n      case 'yearly':\n        return 'năm';\n      default:\n        return 'khoảng thời gian';\n    }\n  };\n\n  // Get summary title\n  const getSummaryTitle = () => {\n    if (periodLabel) {\n      return `Tổng hợp doanh thu ${getPeriodTypeLabel()} ${periodLabel}`;\n    }\n    return `Tổng hợp doanh thu theo ${getPeriodTypeLabel()}`;\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"mb-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold mb-2\",\n        children: getSummaryTitle()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 md:col-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 border-round bg-primary text-primary-contrast\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm mb-2\",\n              children: \"T\\u1ED5ng doanh thu\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold\",\n              children: formatCurrency(totalRevenue)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 md:col-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 border-round bg-blue-500 text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm mb-2\",\n              children: \"T\\u1ED5ng s\\u1ED1 h\\xF3a \\u0111\\u01A1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold\",\n              children: totalInvoices\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_c = StatisticsSummary;\nexport default StatisticsSummary;\nvar _c;\n$RefreshReg$(_c, \"StatisticsSummary\");", "map": {"version": 3, "names": ["React", "Card", "Divider", "jsxDEV", "_jsxDEV", "StatisticsSummary", "data", "periodType", "period<PERSON><PERSON><PERSON>", "totalRevenue", "reduce", "sum", "item", "totalInvoices", "invoiceCount", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "getPeriodTypeLabel", "getSummaryTitle", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/StatisticsSummary.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card } from 'primereact/card';\nimport { Divider } from 'primereact/divider';\nimport { TimeBasedRevenue } from '../../models';\n\ninterface StatisticsSummaryProps {\n  data: TimeBasedRevenue[];\n  periodType: string;\n  periodLabel?: string;\n}\n\nconst StatisticsSummary: React.FC<StatisticsSummaryProps> = ({\n  data,\n  periodType,\n  periodLabel\n}) => {\n  // Calculate total revenue\n  const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);\n\n  // Calculate total invoices\n  const totalInvoices = data.reduce((sum, item) => sum + item.invoiceCount, 0);\n\n  // Format currency\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND',\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  // Get period type label\n  const getPeriodTypeLabel = (): string => {\n    switch (periodType) {\n      case 'daily':\n        return 'ngày';\n      case 'monthly':\n        return 'tháng';\n      case 'quarterly':\n        return 'quý';\n      case 'yearly':\n        return 'năm';\n      default:\n        return 'khoảng thời gian';\n    }\n  };\n\n  // Get summary title\n  const getSummaryTitle = (): string => {\n    if (periodLabel) {\n      return `Tổng hợp doanh thu ${getPeriodTypeLabel()} ${periodLabel}`;\n    }\n    return `Tổng hợp doanh thu theo ${getPeriodTypeLabel()}`;\n  };\n\n  return (\n    <Card className=\"mb-3\">\n      <div className=\"card-header\">\n        <h3 className=\"text-xl font-semibold mb-2\">{getSummaryTitle()}</h3>\n        <Divider />\n      </div>\n\n      <div className=\"card-body\">\n        <div className=\"grid\">\n          <div className=\"col-12 md:col-6\">\n            <div className=\"p-3 border-round bg-primary text-primary-contrast\">\n              <div className=\"text-sm mb-2\">Tổng doanh thu</div>\n              <div className=\"text-3xl font-bold\">{formatCurrency(totalRevenue)}</div>\n            </div>\n          </div>\n\n          <div className=\"col-12 md:col-6\">\n            <div className=\"p-3 border-round bg-blue-500 text-white\">\n              <div className=\"text-sm mb-2\">Tổng số hóa đơn</div>\n              <div className=\"text-3xl font-bold\">{totalInvoices}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default StatisticsSummary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS7C,MAAMC,iBAAmD,GAAGA,CAAC;EAC3DC,IAAI;EACJC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ;EACA,MAAMC,YAAY,GAAGH,IAAI,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACH,YAAY,EAAE,CAAC,CAAC;;EAE3E;EACA,MAAMI,aAAa,GAAGP,IAAI,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACE,YAAY,EAAE,CAAC,CAAC;;EAE5E;EACA,MAAMC,cAAc,GAAIC,KAAa,IAAa;IAChD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACN,KAAK,CAAC;EAClB,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAGA,CAAA,KAAc;IACvC,QAAQhB,UAAU;MAChB,KAAK,OAAO;QACV,OAAO,MAAM;MACf,KAAK,SAAS;QACZ,OAAO,OAAO;MAChB,KAAK,WAAW;QACd,OAAO,KAAK;MACd,KAAK,QAAQ;QACX,OAAO,KAAK;MACd;QACE,OAAO,kBAAkB;IAC7B;EACF,CAAC;;EAED;EACA,MAAMiB,eAAe,GAAGA,CAAA,KAAc;IACpC,IAAIhB,WAAW,EAAE;MACf,OAAO,sBAAsBe,kBAAkB,CAAC,CAAC,IAAIf,WAAW,EAAE;IACpE;IACA,OAAO,2BAA2Be,kBAAkB,CAAC,CAAC,EAAE;EAC1D,CAAC;EAED,oBACEnB,OAAA,CAACH,IAAI;IAACwB,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACpBtB,OAAA;MAAKqB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtB,OAAA;QAAIqB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAEF,eAAe,CAAC;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnE1B,OAAA,CAACF,OAAO;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEN1B,OAAA;MAAKqB,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBtB,OAAA;QAAKqB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBtB,OAAA;UAAKqB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BtB,OAAA;YAAKqB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChEtB,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClD1B,OAAA;cAAKqB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEX,cAAc,CAACN,YAAY;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1B,OAAA;UAAKqB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BtB,OAAA;YAAKqB,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBACtDtB,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnD1B,OAAA;cAAKqB,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEb;YAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACC,EAAA,GAtEI1B,iBAAmD;AAwEzD,eAAeA,iBAAiB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}