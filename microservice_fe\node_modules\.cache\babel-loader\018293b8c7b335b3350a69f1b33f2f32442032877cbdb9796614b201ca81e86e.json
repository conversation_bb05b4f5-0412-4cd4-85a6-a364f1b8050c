{"ast": null, "code": "import _objectSpread from\"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{DatePicker}from'@mui/x-date-pickers/DatePicker';import{formatDateForInput}from'../../utils/dateUtils';import{jsx as _jsx}from\"react/jsx-runtime\";/**\n * Tùy chỉnh DatePicker hiển thị ngày tháng theo định dạng Việt Nam (DD/MM/YYYY)\n */function DatePickerField(_ref){let{label,value,onChange,name,required=false,disabled=false,fullWidth=true,error=false,helperText,sx={},minDate,maxDate}=_ref;// Chuyển đổi giá trị thành đối tượng Date\nconst dateValue=value?new Date(value):null;// <PERSON><PERSON> lý sự kiện thay đổi ngày\nconst handleDateChange=date=>{if(date&&!isNaN(date.getTime())){// Chuyển đổi thành chuỗi YYYY-MM-DD để lưu trữ\nconst formattedDate=formatDateForInput(date);onChange(formattedDate);}else{onChange('');}};return/*#__PURE__*/_jsx(DatePicker,{label:label,value:dateValue,onChange:handleDateChange,format:\"dd/MM/yyyy\",minDate:minDate,maxDate:maxDate,slotProps:{textField:{name,required,disabled,fullWidth,error,helperText,sx:_objectSpread({'& .MuiInputLabel-root':{transform:'translate(14px, -9px) scale(0.75)',background:'#fff',padding:'0 8px'},'& .MuiInputBase-input':{fontFamily:'inherit',fontSize:'1rem'}},sx)}}});};export default DatePickerField;", "map": {"version": 3, "names": ["React", "DatePicker", "formatDateForInput", "jsx", "_jsx", "DatePickerField", "_ref", "label", "value", "onChange", "name", "required", "disabled", "fullWidth", "error", "helperText", "sx", "minDate", "maxDate", "dateValue", "Date", "handleDateChange", "date", "isNaN", "getTime", "formattedDate", "format", "slotProps", "textField", "_objectSpread", "transform", "background", "padding", "fontFamily", "fontSize"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/DatePickerField.tsx"], "sourcesContent": ["import React from 'react';\nimport { DatePicker } from '@mui/x-date-pickers/DatePicker';\nimport { TextFieldProps } from '@mui/material';\nimport { formatDateForInput } from '../../utils/dateUtils';\n\ninterface DatePickerFieldProps {\n  label: string;\n  value: string | Date | undefined;\n  onChange: (date: string) => void;\n  name?: string;\n  required?: boolean;\n  disabled?: boolean;\n  fullWidth?: boolean;\n  error?: boolean;\n  helperText?: string;\n  sx?: any;\n  minDate?: Date;\n  maxDate?: Date;\n}\n\n/**\n * Tùy chỉnh DatePicker hiển thị ngày tháng theo định dạng Việt Nam (DD/MM/YYYY)\n */\nfunction DatePickerField({\n  label,\n  value,\n  onChange,\n  name,\n  required = false,\n  disabled = false,\n  fullWidth = true,\n  error = false,\n  helperText,\n  sx = {},\n  minDate,\n  maxDate,\n}: DatePickerFieldProps) {\n  // Chuyển đổi giá trị thành đối tượng Date\n  const dateValue = value ? new Date(value) : null;\n\n  // Xử lý sự kiện thay đổi ngày\n  const handleDateChange = (date: Date | null) => {\n    if (date && !isNaN(date.getTime())) {\n      // Chuyển đổi thành chuỗi YYYY-MM-DD để lưu trữ\n      const formattedDate = formatDateForInput(date);\n      onChange(formattedDate);\n    } else {\n      onChange('');\n    }\n  };\n\n  return (\n    <DatePicker\n      label={label}\n      value={dateValue}\n      onChange={handleDateChange}\n      format=\"dd/MM/yyyy\"\n      minDate={minDate}\n      maxDate={maxDate}\n      slotProps={{\n        textField: {\n          name,\n          required,\n          disabled,\n          fullWidth,\n          error,\n          helperText,\n          sx: {\n            '& .MuiInputLabel-root': {\n              transform: 'translate(14px, -9px) scale(0.75)',\n              background: '#fff',\n              padding: '0 8px'\n            },\n            '& .MuiInputBase-input': {\n              fontFamily: 'inherit',\n              fontSize: '1rem'\n            },\n            ...sx\n          } as TextFieldProps['sx'],\n        }\n      }}\n    />\n  );\n};\n\nexport default DatePickerField;\n"], "mappings": "gKAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,UAAU,KAAQ,gCAAgC,CAE3D,OAASC,kBAAkB,KAAQ,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAiB3D;AACA;AACA,GACA,QAAS,CAAAC,eAAeA,CAAAC,IAAA,CAaC,IAbA,CACvBC,KAAK,CACLC,KAAK,CACLC,QAAQ,CACRC,IAAI,CACJC,QAAQ,CAAG,KAAK,CAChBC,QAAQ,CAAG,KAAK,CAChBC,SAAS,CAAG,IAAI,CAChBC,KAAK,CAAG,KAAK,CACbC,UAAU,CACVC,EAAE,CAAG,CAAC,CAAC,CACPC,OAAO,CACPC,OACoB,CAAC,CAAAZ,IAAA,CACrB;AACA,KAAM,CAAAa,SAAS,CAAGX,KAAK,CAAG,GAAI,CAAAY,IAAI,CAACZ,KAAK,CAAC,CAAG,IAAI,CAEhD;AACA,KAAM,CAAAa,gBAAgB,CAAIC,IAAiB,EAAK,CAC9C,GAAIA,IAAI,EAAI,CAACC,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAAE,CAClC;AACA,KAAM,CAAAC,aAAa,CAAGvB,kBAAkB,CAACoB,IAAI,CAAC,CAC9Cb,QAAQ,CAACgB,aAAa,CAAC,CACzB,CAAC,IAAM,CACLhB,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAAC,CAED,mBACEL,IAAA,CAACH,UAAU,EACTM,KAAK,CAAEA,KAAM,CACbC,KAAK,CAAEW,SAAU,CACjBV,QAAQ,CAAEY,gBAAiB,CAC3BK,MAAM,CAAC,YAAY,CACnBT,OAAO,CAAEA,OAAQ,CACjBC,OAAO,CAAEA,OAAQ,CACjBS,SAAS,CAAE,CACTC,SAAS,CAAE,CACTlB,IAAI,CACJC,QAAQ,CACRC,QAAQ,CACRC,SAAS,CACTC,KAAK,CACLC,UAAU,CACVC,EAAE,CAAAa,aAAA,EACA,uBAAuB,CAAE,CACvBC,SAAS,CAAE,mCAAmC,CAC9CC,UAAU,CAAE,MAAM,CAClBC,OAAO,CAAE,OACX,CAAC,CACD,uBAAuB,CAAE,CACvBC,UAAU,CAAE,SAAS,CACrBC,QAAQ,CAAE,MACZ,CAAC,EACElB,EAAE,CAET,CACF,CAAE,CACH,CAAC,CAEN,CAAC,CAED,cAAe,CAAAX,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}