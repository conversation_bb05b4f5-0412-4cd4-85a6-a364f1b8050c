{"ast": null, "code": "import React from'react';import{Box,FormControl,InputLabel,Select,MenuItem,Typography,Paper,Grid,ToggleButton,ToggleButtonGroup}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TimeBasedStatisticsSelector=_ref=>{let{periodType,// Các props không sử dụng trực tiếp nhưng vẫn cần để truyền từ component cha\n// startDate,\n// endDate,\n// selectedDay,\nselectedYear,onPeriodTypeChange,// onStartDateChange,\n// onEndDateChange,\n// onDayChange,\n// onMonthChange,\nonYearChange// onApplyFilter\n}=_ref;// Using the centralized formatDateForDisplay function from dateUtils.ts\nconst handlePeriodTypeChange=(_,newPeriodType)=>{if(newPeriodType!==null){// Chỉ gọi onPeriodTypeChange, việc áp dụng filter sẽ được xử lý ở component cha\nonPeriodTypeChange(newPeriodType);}};const handleYearChange=event=>{// Chỉ gọi onYearChange, việc áp dụng filter sẽ được xử lý ở component cha\nonYearChange(Number(event.target.value));};// Generate years options (current year - 5 to current year + 5)\nconst currentYear=new Date().getFullYear();const yearsOptions=Array.from({length:11},(_,i)=>currentYear-5+i);return/*#__PURE__*/_jsxs(Paper,{elevation:1,sx:{p:3,mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Th\\u1ED1ng k\\xEA doanh thu theo th\\u1EDDi gian\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,alignItems:\"center\",sx:{mb:3},children:/*#__PURE__*/_jsx(Box,{sx:{p:1},children:/*#__PURE__*/_jsxs(ToggleButtonGroup,{value:periodType,exclusive:true,onChange:handlePeriodTypeChange,\"aria-label\":\"Lo\\u1EA1i th\\u1EDDi gian\",children:[/*#__PURE__*/_jsx(ToggleButton,{value:\"monthly\",\"aria-label\":\"Theo th\\xE1ng\",sx:{px:3},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:40,height:40,borderRadius:'50%',bgcolor:periodType==='monthly'?'primary.main':'grey.200',display:'flex',alignItems:'center',justifyContent:'center',mb:1},children:/*#__PURE__*/_jsx(Box,{sx:{width:20,height:20,borderRadius:'50%',bgcolor:'white'}})}),/*#__PURE__*/_jsx(Typography,{children:\"Theo th\\xE1ng\"})]})}),/*#__PURE__*/_jsx(ToggleButton,{value:\"quarterly\",\"aria-label\":\"Theo qu\\xFD\",sx:{px:3},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:40,height:40,borderRadius:'50%',bgcolor:periodType==='quarterly'?'primary.main':'grey.200',display:'flex',alignItems:'center',justifyContent:'center',mb:1},children:/*#__PURE__*/_jsx(Box,{sx:{width:20,height:20,borderRadius:'50%',bgcolor:'white'}})}),/*#__PURE__*/_jsx(Typography,{children:\"Theo qu\\xFD\"})]})}),/*#__PURE__*/_jsx(ToggleButton,{value:\"yearly\",\"aria-label\":\"Theo n\\u0103m\",sx:{px:3},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:40,height:40,borderRadius:'50%',bgcolor:periodType==='yearly'?'primary.main':'grey.200',display:'flex',alignItems:'center',justifyContent:'center',mb:1},children:/*#__PURE__*/_jsx(Box,{sx:{width:20,height:20,borderRadius:'50%',bgcolor:'white'}})}),/*#__PURE__*/_jsx(Typography,{children:\"Theo n\\u0103m\"})]})})]})})}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,alignItems:\"flex-end\",children:(periodType==='monthly'||periodType==='quarterly')&&/*#__PURE__*/_jsx(Box,{sx:{width:'100%',p:1},children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{id:\"year-select-label\",children:\"N\\u0103m\"}),/*#__PURE__*/_jsx(Select,{labelId:\"year-select-label\",id:\"year-select\",value:selectedYear.toString(),label:\"N\\u0103m\",onChange:handleYearChange,children:yearsOptions.map(year=>/*#__PURE__*/_jsx(MenuItem,{value:year,children:year},\"year-\".concat(year)))})]})})})]});};export default TimeBasedStatisticsSelector;", "map": {"version": 3, "names": ["React", "Box", "FormControl", "InputLabel", "Select", "MenuItem", "Typography", "Paper", "Grid", "ToggleButton", "ToggleButtonGroup", "jsx", "_jsx", "jsxs", "_jsxs", "TimeBasedStatisticsSelector", "_ref", "periodType", "selected<PERSON>ear", "onPeriodTypeChange", "onYearChange", "handlePeriodTypeChange", "_", "newPeriodType", "handleYearChange", "event", "Number", "target", "value", "currentYear", "Date", "getFullYear", "yearsOptions", "Array", "from", "length", "i", "elevation", "sx", "p", "mb", "children", "variant", "gutterBottom", "container", "spacing", "alignItems", "exclusive", "onChange", "px", "display", "flexDirection", "width", "height", "borderRadius", "bgcolor", "justifyContent", "fullWidth", "id", "labelId", "toString", "label", "map", "year", "concat"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/TimeBasedStatisticsSelector.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  SelectChangeEvent,\n\n  Typography,\n  Paper,\n  Grid,\n  ToggleButton,\n  ToggleButtonGroup,\n\n} from '@mui/material';\n\n\ninterface TimeBasedStatisticsSelectorProps {\n  periodType: string;\n  startDate: Date;\n  endDate: Date;\n  selectedDay: number;\n  selectedMonth: number;\n  selectedYear: number;\n  onPeriodTypeChange: (periodType: string) => void;\n  onStartDateChange: (date: Date) => void;\n  onEndDateChange: (date: Date) => void;\n  onDayChange: (day: number) => void;\n  onMonthChange: (month: number) => void;\n  onYearChange: (year: number) => void;\n  onApplyFilter?: () => void;\n}\n\nconst TimeBasedStatisticsSelector: React.FC<TimeBasedStatisticsSelectorProps> = ({\n  periodType,\n  // Các props không sử dụng trực tiếp nhưng vẫn cần để truyền từ component cha\n  // startDate,\n  // endDate,\n  // selectedDay,\n  selectedYear,\n  onPeriodTypeChange,\n  // onStartDateChange,\n  // onEndDateChange,\n  // onDayChange,\n  // onMonthChange,\n  onYearChange,\n  // onApplyFilter\n}) => {\n  // Using the centralized formatDateForDisplay function from dateUtils.ts\n\n  const handlePeriodTypeChange = (\n    _: React.MouseEvent<HTMLElement>,\n    newPeriodType: string,\n  ) => {\n    if (newPeriodType !== null) {\n      // Chỉ gọi onPeriodTypeChange, việc áp dụng filter sẽ được xử lý ở component cha\n      onPeriodTypeChange(newPeriodType);\n    }\n  };\n\n  const handleYearChange = (event: SelectChangeEvent) => {\n    // Chỉ gọi onYearChange, việc áp dụng filter sẽ được xử lý ở component cha\n    onYearChange(Number(event.target.value));\n  };\n\n  // Generate years options (current year - 5 to current year + 5)\n  const currentYear = new Date().getFullYear();\n  const yearsOptions = Array.from(\n    { length: 11 },\n    (_, i) => currentYear - 5 + i\n  );\n\n  return (\n    <Paper elevation={1} sx={{ p: 3, mb: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Thống kê doanh thu theo thời gian\n      </Typography>\n\n      <Grid container spacing={2} alignItems=\"center\" sx={{ mb: 3 }}>\n        <Box sx={{ p: 1 }}>\n          <ToggleButtonGroup\n            value={periodType}\n            exclusive\n            onChange={handlePeriodTypeChange}\n            aria-label=\"Loại thời gian\"\n          >\n            <ToggleButton value=\"monthly\" aria-label=\"Theo tháng\" sx={{ px: 3 }}>\n              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n                <Box\n                  sx={{\n                    width: 40,\n                    height: 40,\n                    borderRadius: '50%',\n                    bgcolor: periodType === 'monthly' ? 'primary.main' : 'grey.200',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    mb: 1\n                  }}\n                >\n                  <Box\n                    sx={{\n                      width: 20,\n                      height: 20,\n                      borderRadius: '50%',\n                      bgcolor: 'white'\n                    }}\n                  />\n                </Box>\n                <Typography>Theo tháng</Typography>\n              </Box>\n            </ToggleButton>\n            <ToggleButton value=\"quarterly\" aria-label=\"Theo quý\" sx={{ px: 3 }}>\n              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n                <Box\n                  sx={{\n                    width: 40,\n                    height: 40,\n                    borderRadius: '50%',\n                    bgcolor: periodType === 'quarterly' ? 'primary.main' : 'grey.200',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    mb: 1\n                  }}\n                >\n                  <Box\n                    sx={{\n                      width: 20,\n                      height: 20,\n                      borderRadius: '50%',\n                      bgcolor: 'white'\n                    }}\n                  />\n                </Box>\n                <Typography>Theo quý</Typography>\n              </Box>\n            </ToggleButton>\n            <ToggleButton value=\"yearly\" aria-label=\"Theo năm\" sx={{ px: 3 }}>\n              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n                <Box\n                  sx={{\n                    width: 40,\n                    height: 40,\n                    borderRadius: '50%',\n                    bgcolor: periodType === 'yearly' ? 'primary.main' : 'grey.200',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    mb: 1\n                  }}\n                >\n                  <Box\n                    sx={{\n                      width: 20,\n                      height: 20,\n                      borderRadius: '50%',\n                      bgcolor: 'white'\n                    }}\n                  />\n                </Box>\n                <Typography>Theo năm</Typography>\n              </Box>\n            </ToggleButton>\n          </ToggleButtonGroup>\n        </Box>\n      </Grid>\n\n      <Grid container spacing={2} alignItems=\"flex-end\">\n        {(periodType === 'monthly' || periodType === 'quarterly') && (\n          <Box sx={{ width: '100%', p: 1 }}>\n            <FormControl fullWidth>\n              <InputLabel id=\"year-select-label\">Năm</InputLabel>\n              <Select\n                labelId=\"year-select-label\"\n                id=\"year-select\"\n                value={selectedYear.toString()}\n                label=\"Năm\"\n                onChange={handleYearChange}\n              >\n                {yearsOptions.map(year => (\n                  <MenuItem key={`year-${year}`} value={year}>{year}</MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Box>\n        )}\n      </Grid>\n    </Paper>\n  );\n};\n\nexport default TimeBasedStatisticsSelector;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,GAAG,CACHC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CAGRC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,YAAY,CACZC,iBAAiB,KAEZ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAmBvB,KAAM,CAAAC,2BAAuE,CAAGC,IAAA,EAc1E,IAd2E,CAC/EC,UAAU,CACV;AACA;AACA;AACA;AACAC,YAAY,CACZC,kBAAkB,CAClB;AACA;AACA;AACA;AACAC,YACA;AACF,CAAC,CAAAJ,IAAA,CACC;AAEA,KAAM,CAAAK,sBAAsB,CAAGA,CAC7BC,CAAgC,CAChCC,aAAqB,GAClB,CACH,GAAIA,aAAa,GAAK,IAAI,CAAE,CAC1B;AACAJ,kBAAkB,CAACI,aAAa,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIC,KAAwB,EAAK,CACrD;AACAL,YAAY,CAACM,MAAM,CAACD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAC1C,CAAC,CAED;AACA,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC5C,KAAM,CAAAC,YAAY,CAAGC,KAAK,CAACC,IAAI,CAC7B,CAAEC,MAAM,CAAE,EAAG,CAAC,CACd,CAACb,CAAC,CAAEc,CAAC,GAAKP,WAAW,CAAG,CAAC,CAAGO,CAC9B,CAAC,CAED,mBACEtB,KAAA,CAACP,KAAK,EAAC8B,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACvC7B,IAAA,CAACN,UAAU,EAACoC,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,gDAEtC,CAAY,CAAC,cAEb7B,IAAA,CAACJ,IAAI,EAACoC,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,UAAU,CAAC,QAAQ,CAACR,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cAC5D7B,IAAA,CAACX,GAAG,EAACqC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,cAChB3B,KAAA,CAACJ,iBAAiB,EAChBkB,KAAK,CAAEX,UAAW,CAClB8B,SAAS,MACTC,QAAQ,CAAE3B,sBAAuB,CACjC,aAAW,0BAAgB,CAAAoB,QAAA,eAE3B7B,IAAA,CAACH,YAAY,EAACmB,KAAK,CAAC,SAAS,CAAC,aAAW,eAAY,CAACU,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,cAClE3B,KAAA,CAACb,GAAG,EAACqC,EAAE,CAAE,CAAEY,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEL,UAAU,CAAE,QAAS,CAAE,CAAAL,QAAA,eAC1E7B,IAAA,CAACX,GAAG,EACFqC,EAAE,CAAE,CACFc,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAEtC,UAAU,GAAK,SAAS,CAAG,cAAc,CAAG,UAAU,CAC/DiC,OAAO,CAAE,MAAM,CACfJ,UAAU,CAAE,QAAQ,CACpBU,cAAc,CAAE,QAAQ,CACxBhB,EAAE,CAAE,CACN,CAAE,CAAAC,QAAA,cAEF7B,IAAA,CAACX,GAAG,EACFqC,EAAE,CAAE,CACFc,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,OACX,CAAE,CACH,CAAC,CACC,CAAC,cACN3C,IAAA,CAACN,UAAU,EAAAmC,QAAA,CAAC,eAAU,CAAY,CAAC,EAChC,CAAC,CACM,CAAC,cACf7B,IAAA,CAACH,YAAY,EAACmB,KAAK,CAAC,WAAW,CAAC,aAAW,aAAU,CAACU,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,cAClE3B,KAAA,CAACb,GAAG,EAACqC,EAAE,CAAE,CAAEY,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEL,UAAU,CAAE,QAAS,CAAE,CAAAL,QAAA,eAC1E7B,IAAA,CAACX,GAAG,EACFqC,EAAE,CAAE,CACFc,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAEtC,UAAU,GAAK,WAAW,CAAG,cAAc,CAAG,UAAU,CACjEiC,OAAO,CAAE,MAAM,CACfJ,UAAU,CAAE,QAAQ,CACpBU,cAAc,CAAE,QAAQ,CACxBhB,EAAE,CAAE,CACN,CAAE,CAAAC,QAAA,cAEF7B,IAAA,CAACX,GAAG,EACFqC,EAAE,CAAE,CACFc,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,OACX,CAAE,CACH,CAAC,CACC,CAAC,cACN3C,IAAA,CAACN,UAAU,EAAAmC,QAAA,CAAC,aAAQ,CAAY,CAAC,EAC9B,CAAC,CACM,CAAC,cACf7B,IAAA,CAACH,YAAY,EAACmB,KAAK,CAAC,QAAQ,CAAC,aAAW,eAAU,CAACU,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,cAC/D3B,KAAA,CAACb,GAAG,EAACqC,EAAE,CAAE,CAAEY,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEL,UAAU,CAAE,QAAS,CAAE,CAAAL,QAAA,eAC1E7B,IAAA,CAACX,GAAG,EACFqC,EAAE,CAAE,CACFc,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAEtC,UAAU,GAAK,QAAQ,CAAG,cAAc,CAAG,UAAU,CAC9DiC,OAAO,CAAE,MAAM,CACfJ,UAAU,CAAE,QAAQ,CACpBU,cAAc,CAAE,QAAQ,CACxBhB,EAAE,CAAE,CACN,CAAE,CAAAC,QAAA,cAEF7B,IAAA,CAACX,GAAG,EACFqC,EAAE,CAAE,CACFc,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,OACX,CAAE,CACH,CAAC,CACC,CAAC,cACN3C,IAAA,CAACN,UAAU,EAAAmC,QAAA,CAAC,eAAQ,CAAY,CAAC,EAC9B,CAAC,CACM,CAAC,EACE,CAAC,CACjB,CAAC,CACF,CAAC,cAEP7B,IAAA,CAACJ,IAAI,EAACoC,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,UAAU,CAAC,UAAU,CAAAL,QAAA,CAC9C,CAACxB,UAAU,GAAK,SAAS,EAAIA,UAAU,GAAK,WAAW,gBACtDL,IAAA,CAACX,GAAG,EAACqC,EAAE,CAAE,CAAEc,KAAK,CAAE,MAAM,CAAEb,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,cAC/B3B,KAAA,CAACZ,WAAW,EAACuD,SAAS,MAAAhB,QAAA,eACpB7B,IAAA,CAACT,UAAU,EAACuD,EAAE,CAAC,mBAAmB,CAAAjB,QAAA,CAAC,UAAG,CAAY,CAAC,cACnD7B,IAAA,CAACR,MAAM,EACLuD,OAAO,CAAC,mBAAmB,CAC3BD,EAAE,CAAC,aAAa,CAChB9B,KAAK,CAAEV,YAAY,CAAC0C,QAAQ,CAAC,CAAE,CAC/BC,KAAK,CAAC,UAAK,CACXb,QAAQ,CAAExB,gBAAiB,CAAAiB,QAAA,CAE1BT,YAAY,CAAC8B,GAAG,CAACC,IAAI,eACpBnD,IAAA,CAACP,QAAQ,EAAsBuB,KAAK,CAAEmC,IAAK,CAAAtB,QAAA,CAAEsB,IAAI,UAAAC,MAAA,CAA1BD,IAAI,CAAiC,CAC7D,CAAC,CACI,CAAC,EACE,CAAC,CACX,CACN,CACG,CAAC,EACF,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAhD,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}