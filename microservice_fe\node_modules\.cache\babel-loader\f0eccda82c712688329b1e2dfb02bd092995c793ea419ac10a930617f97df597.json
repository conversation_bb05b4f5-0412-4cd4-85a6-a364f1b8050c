{"ast": null, "code": "import React from'react';import{Typography,Box,Divider}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function PageHeader(_ref){let{title,subtitle}=_ref;return/*#__PURE__*/_jsxs(Box,{sx:{mb:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:title}),subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",children:subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mt:2}})]});};export default PageHeader;", "map": {"version": 3, "names": ["React", "Typography", "Box", "Divider", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "title", "subtitle", "sx", "mb", "children", "variant", "component", "gutterBottom", "color", "mt"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/PageHeader.tsx"], "sourcesContent": ["import React from 'react';\nimport { Typography, Box, Divider } from '@mui/material';\n\ninterface PageHeaderProps {\n  title: string;\n  subtitle?: string;\n}\n\nfunction PageHeader({ title, subtitle }: PageHeaderProps) {\n  return (\n    <Box sx={{ mb: 4 }}>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        {title}\n      </Typography>\n      {subtitle && (\n        <Typography variant=\"subtitle1\" color=\"text.secondary\">\n          {subtitle}\n        </Typography>\n      )}\n      <Divider sx={{ mt: 2 }} />\n    </Box>\n  );\n};\n\nexport default PageHeader;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,UAAU,CAAEC,GAAG,CAAEC,OAAO,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOzD,QAAS,CAAAC,UAAUA,CAAAC,IAAA,CAAuC,IAAtC,CAAEC,KAAK,CAAEC,QAA0B,CAAC,CAAAF,IAAA,CACtD,mBACEF,KAAA,CAACL,GAAG,EAACU,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACjBT,IAAA,CAACJ,UAAU,EAACc,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAH,QAAA,CACjDJ,KAAK,CACI,CAAC,CACZC,QAAQ,eACPN,IAAA,CAACJ,UAAU,EAACc,OAAO,CAAC,WAAW,CAACG,KAAK,CAAC,gBAAgB,CAAAJ,QAAA,CACnDH,QAAQ,CACC,CACb,cACDN,IAAA,CAACF,OAAO,EAACS,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,EACvB,CAAC,CAEV,CAAC,CAED,cAAe,CAAAX,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}