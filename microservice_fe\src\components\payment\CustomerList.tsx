import React from 'react';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,

  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Tooltip,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Card,
  CardContent,

} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';

import PaymentIcon from '@mui/icons-material/Payment';
import { Customer } from '../../models';

interface CustomerListProps {
  customers: Customer[];
  onSelectCustomer: (customer: Customer) => void;
  onSearch: (term: string) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  loading: boolean;
}

const CustomerList: React.FC<CustomerListProps> = ({
  customers,
  onSelectCustomer,
  onSearch,
  searchTerm,
  setSearchTerm,
  loading,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchTerm);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      onSearch(searchTerm);
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Danh sách khách hàng
      </Typography>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box
          component="form"
          onSubmit={handleSearchSubmit}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            flexDirection: isMobile ? 'column' : 'row'
          }}
        >
          <TextField
            fullWidth
            placeholder="Tìm kiếm theo tên hoặc số điện thoại"
            value={searchTerm}
            onChange={handleSearchChange}
            onKeyPress={handleKeyPress}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
          />
          <Button
            variant="contained"
            color="primary"
            onClick={() => onSearch(searchTerm)}
            disabled={loading}
            sx={{ minWidth: '120px', height: '56px' }}
          >
            {loading ? <CircularProgress size={24} color="inherit" /> : 'Tìm kiếm'}
          </Button>
        </Box>
      </Paper>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : customers.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            Không tìm thấy khách hàng nào
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Vui lòng thử tìm kiếm với từ khóa khác
          </Typography>
        </Paper>
      ) : isMobile ? (
        // Mobile view - card list
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {customers.map((customer) => (
            <Card key={customer.id} sx={{ width: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="h6" component="div">
                      {customer.fullName}
                    </Typography>
                    {customer.companyName && (
                      <Typography variant="body2" color="text.secondary">
                        {customer.companyName}
                      </Typography>
                    )}
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      SĐT: {customer.phoneNumber}
                    </Typography>
                    {customer.address && (
                      <Typography variant="body2">
                        Địa chỉ: {customer.address}
                      </Typography>
                    )}
                  </Box>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<PaymentIcon />}
                    onClick={() => onSelectCustomer(customer)}
                    size="small"
                  >
                    Thanh toán
                  </Button>
                </Box>
              </CardContent>
            </Card>
          ))}
        </Box>
      ) : (
        // Desktop view - table
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Tên khách hàng</TableCell>
                <TableCell>Tên doanh nghiệp</TableCell>
                <TableCell>Số điện thoại</TableCell>
                <TableCell>Địa chỉ</TableCell>
                <TableCell align="center">Thao tác</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {customers.map((customer) => (
                <TableRow key={customer.id} hover>
                  <TableCell>{customer.fullName}</TableCell>
                  <TableCell>{customer.companyName || '-'}</TableCell>
                  <TableCell>{customer.phoneNumber}</TableCell>
                  <TableCell>{customer.address || '-'}</TableCell>
                  <TableCell align="center">
                    <Tooltip title="Xem hợp đồng và thanh toán">
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<PaymentIcon />}
                        onClick={() => onSelectCustomer(customer)}
                        size="small"
                      >
                        Thanh toán
                      </Button>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default CustomerList;
