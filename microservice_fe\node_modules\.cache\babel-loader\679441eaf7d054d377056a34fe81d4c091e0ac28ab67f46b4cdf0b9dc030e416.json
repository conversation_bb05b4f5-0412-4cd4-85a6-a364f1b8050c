{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\pages\\\\ContractDetailsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, Button, Chip } from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport EditIcon from '@mui/icons-material/Edit';\nimport { ContractDetails } from '../components/contract';\nimport { ContractStatusMap } from '../models';\nimport { contractService } from '../services/contract/contractService';\nimport { LoadingSpinner, ErrorAlert, PageHeader } from '../components/common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ContractDetailsPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [contract, setContract] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const fetchContract = useCallback(async () => {\n    if (!id) return;\n    setLoading(true);\n    try {\n      const data = await contractService.getContractById(parseInt(id, 10));\n      setContract(data);\n    } catch (err) {\n      setError(err.message || 'Đã xảy ra lỗi khi tải thông tin hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n  useEffect(() => {\n    fetchContract();\n  }, [id]);\n\n  // Listen for refresh flag from localStorage (for payment updates)\n  useEffect(() => {\n    const handleStorageChange = () => {\n      const needsRefresh = localStorage.getItem('contractsListNeedsRefresh');\n      if (needsRefresh === 'true') {\n        // Don't remove the flag here, let ContractsListPage handle it\n        fetchContract(); // Refresh current contract details\n      }\n    };\n\n    // Listen for storage changes\n    window.addEventListener('storage', handleStorageChange);\n\n    // Also listen for focus events (when user returns to tab)\n    window.addEventListener('focus', handleStorageChange);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('focus', handleStorageChange);\n    };\n  }, [id]);\n  const handleBack = () => {\n    navigate('/contracts');\n  };\n  const handleEdit = () => {\n    navigate(`/contracts/edit/${id}`);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      fullScreen: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 12\n    }, this);\n  }\n  if (!contract) {\n    return /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: \"Kh\\xF4ng t\\xECm th\\u1EA5y h\\u1EE3p \\u0111\\u1ED3ng\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 12\n    }, this);\n  }\n  const getStatusColor = status => {\n    switch (status) {\n      case 0:\n        // Pending\n        return 'warning';\n      case 1:\n        // Active\n        return 'success';\n      case 2:\n        // Completed\n        return 'info';\n      case 3:\n        // Cancelled\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 28\n        }, this),\n        onClick: handleBack,\n        children: \"Quay l\\u1EA1i danh s\\xE1ch h\\u1EE3p \\u0111\\u1ED3ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 48\n        }, this),\n        onClick: handleEdit,\n        children: \"Ch\\u1EC9nh s\\u1EEDa h\\u1EE3p \\u0111\\u1ED3ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n        title: `Hợp đồng #${contract.id}`,\n        subtitle: `Khách hàng: ${contract.customerName}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        label: ContractStatusMap[contract.status || 0],\n        color: getStatusColor(contract.status || 0),\n        sx: {\n          fontSize: '1rem',\n          py: 1,\n          px: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContractDetails, {\n      contract: contract\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(ContractDetailsPage, \"wAdnepiRt5825ojtJv8D7BbU70w=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ContractDetailsPage;\nexport default ContractDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"ContractDetailsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "Box", "<PERSON><PERSON>", "Chip", "ArrowBackIcon", "EditIcon", "ContractDetails", "ContractStatusMap", "contractService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ContractDetailsPage", "_s", "id", "navigate", "contract", "setContract", "loading", "setLoading", "error", "setError", "fetchContract", "data", "getContractById", "parseInt", "err", "message", "handleStorageChange", "needsRefresh", "localStorage", "getItem", "window", "addEventListener", "removeEventListener", "handleBack", "handleEdit", "fullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "status", "children", "sx", "display", "justifyContent", "alignItems", "mb", "startIcon", "onClick", "variant", "title", "subtitle", "customerName", "label", "color", "fontSize", "py", "px", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/pages/ContractDetailsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, Button, Chip } from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport EditIcon from '@mui/icons-material/Edit';\nimport { ContractDetails } from '../components/contract';\nimport { CustomerContract, ContractStatusMap } from '../models';\nimport { contractService } from '../services/contract/contractService';\nimport { Loading<PERSON>pinner, ErrorAlert, PageHeader } from '../components/common';\n\nconst ContractDetailsPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const [contract, setContract] = useState<CustomerContract | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchContract = useCallback(async () => {\n    if (!id) return;\n\n    setLoading(true);\n    try {\n      const data = await contractService.getContractById(parseInt(id, 10));\n      setContract(data);\n    } catch (err: any) {\n      setError(err.message || 'Đã xảy ra lỗi khi tải thông tin hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n\n  useEffect(() => {\n    fetchContract();\n  }, [id]);\n\n  // Listen for refresh flag from localStorage (for payment updates)\n  useEffect(() => {\n    const handleStorageChange = () => {\n      const needsRefresh = localStorage.getItem('contractsListNeedsRefresh');\n      if (needsRefresh === 'true') {\n        // Don't remove the flag here, let ContractsListPage handle it\n        fetchContract(); // Refresh current contract details\n      }\n    };\n\n    // Listen for storage changes\n    window.addEventListener('storage', handleStorageChange);\n\n    // Also listen for focus events (when user returns to tab)\n    window.addEventListener('focus', handleStorageChange);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('focus', handleStorageChange);\n    };\n  }, [id]);\n\n  const handleBack = () => {\n    navigate('/contracts');\n  };\n\n  const handleEdit = () => {\n    navigate(`/contracts/edit/${id}`);\n  };\n\n  if (loading) {\n    return <LoadingSpinner fullScreen />;\n  }\n\n  if (error) {\n    return <ErrorAlert message={error} />;\n  }\n\n  if (!contract) {\n    return <ErrorAlert message=\"Không tìm thấy hợp đồng\" />;\n  }\n\n  const getStatusColor = (status: number) => {\n    switch (status) {\n      case 0: // Pending\n        return 'warning';\n      case 1: // Active\n        return 'success';\n      case 2: // Completed\n        return 'info';\n      case 3: // Cancelled\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Button startIcon={<ArrowBackIcon />} onClick={handleBack}>\n          Quay lại danh sách hợp đồng\n        </Button>\n        <Button variant=\"contained\" startIcon={<EditIcon />} onClick={handleEdit}>\n          Chỉnh sửa hợp đồng\n        </Button>\n      </Box>\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <PageHeader\n          title={`Hợp đồng #${contract.id}`}\n          subtitle={`Khách hàng: ${contract.customerName}`}\n        />\n        <Chip\n          label={ContractStatusMap[contract.status || 0]}\n          color={getStatusColor(contract.status || 0)}\n          sx={{ fontSize: '1rem', py: 1, px: 2 }}\n        />\n      </Box>\n\n      <ContractDetails contract={contract} />\n    </Box>\n  );\n};\n\nexport default ContractDetailsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,GAAG,EAAEC,MAAM,EAAEC,IAAI,QAAQ,eAAe;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAA2BC,iBAAiB,QAAQ,WAAW;AAC/D,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,cAAc,EAAEC,UAAU,EAAEC,UAAU,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM;IAAEC;EAAG,CAAC,GAAGjB,SAAS,CAAiB,CAAC;EAC1C,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAA0B,IAAI,CAAC;EACvE,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAM4B,aAAa,GAAG1B,WAAW,CAAC,YAAY;IAC5C,IAAI,CAACkB,EAAE,EAAE;IAETK,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,IAAI,GAAG,MAAMjB,eAAe,CAACkB,eAAe,CAACC,QAAQ,CAACX,EAAE,EAAE,EAAE,CAAC,CAAC;MACpEG,WAAW,CAACM,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOG,GAAQ,EAAE;MACjBL,QAAQ,CAACK,GAAG,CAACC,OAAO,IAAI,0CAA0C,CAAC;IACrE,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACL,EAAE,CAAC,CAAC;EAERnB,SAAS,CAAC,MAAM;IACd2B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACR,EAAE,CAAC,CAAC;;EAER;EACAnB,SAAS,CAAC,MAAM;IACd,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;MAChC,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;MACtE,IAAIF,YAAY,KAAK,MAAM,EAAE;QAC3B;QACAP,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB;IACF,CAAC;;IAED;IACAU,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEL,mBAAmB,CAAC;;IAEvD;IACAI,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEL,mBAAmB,CAAC;IAErD,OAAO,MAAM;MACXI,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEN,mBAAmB,CAAC;MAC1DI,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEN,mBAAmB,CAAC;IAC1D,CAAC;EACH,CAAC,EAAE,CAACd,EAAE,CAAC,CAAC;EAER,MAAMqB,UAAU,GAAGA,CAAA,KAAM;IACvBpB,QAAQ,CAAC,YAAY,CAAC;EACxB,CAAC;EAED,MAAMqB,UAAU,GAAGA,CAAA,KAAM;IACvBrB,QAAQ,CAAC,mBAAmBD,EAAE,EAAE,CAAC;EACnC,CAAC;EAED,IAAII,OAAO,EAAE;IACX,oBAAOP,OAAA,CAACJ,cAAc;MAAC8B,UAAU;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtC;EAEA,IAAIrB,KAAK,EAAE;IACT,oBAAOT,OAAA,CAACH,UAAU;MAACmB,OAAO,EAAEP;IAAM;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACvC;EAEA,IAAI,CAACzB,QAAQ,EAAE;IACb,oBAAOL,OAAA,CAACH,UAAU;MAACmB,OAAO,EAAC;IAAyB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzD;EAEA,MAAMC,cAAc,GAAIC,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,CAAC;QAAE;QACN,OAAO,SAAS;MAClB,KAAK,CAAC;QAAE;QACN,OAAO,SAAS;MAClB,KAAK,CAAC;QAAE;QACN,OAAO,MAAM;MACf,KAAK,CAAC;QAAE;QACN,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACEhC,OAAA,CAACZ,GAAG;IAAA6C,QAAA,gBACFjC,OAAA,CAACZ,GAAG;MAAC8C,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzFjC,OAAA,CAACX,MAAM;QAACkD,SAAS,eAAEvC,OAAA,CAACT,aAAa;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACU,OAAO,EAAEhB,UAAW;QAAAS,QAAA,EAAC;MAE3D;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9B,OAAA,CAACX,MAAM;QAACoD,OAAO,EAAC,WAAW;QAACF,SAAS,eAAEvC,OAAA,CAACR,QAAQ;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACU,OAAO,EAAEf,UAAW;QAAAQ,QAAA,EAAC;MAE1E;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN9B,OAAA,CAACZ,GAAG;MAAC8C,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzFjC,OAAA,CAACF,UAAU;QACT4C,KAAK,EAAE,aAAarC,QAAQ,CAACF,EAAE,EAAG;QAClCwC,QAAQ,EAAE,eAAetC,QAAQ,CAACuC,YAAY;MAAG;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACF9B,OAAA,CAACV,IAAI;QACHuD,KAAK,EAAEnD,iBAAiB,CAACW,QAAQ,CAAC2B,MAAM,IAAI,CAAC,CAAE;QAC/Cc,KAAK,EAAEf,cAAc,CAAC1B,QAAQ,CAAC2B,MAAM,IAAI,CAAC,CAAE;QAC5CE,EAAE,EAAE;UAAEa,QAAQ,EAAE,MAAM;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN9B,OAAA,CAACP,eAAe;MAACY,QAAQ,EAAEA;IAAS;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpC,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA5GID,mBAA6B;EAAA,QAClBf,SAAS,EACPC,WAAW;AAAA;AAAA+D,EAAA,GAFxBjD,mBAA6B;AA8GnC,eAAeA,mBAAmB;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}