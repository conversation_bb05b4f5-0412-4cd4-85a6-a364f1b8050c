{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\statistics\\\\StatisticsSummary.tsx\";\nimport React from 'react';\nimport { Box, Paper, Typography, Grid, Divider } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatisticsSummary = ({\n  data,\n  periodType,\n  periodLabel\n}) => {\n  // Calculate total revenue\n  const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);\n\n  // Calculate total invoices\n  const totalInvoices = data.reduce((sum, item) => sum + item.invoiceCount, 0);\n\n  // Format currency\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND',\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  // Get period type label\n  const getPeriodTypeLabel = () => {\n    switch (periodType) {\n      case 'daily':\n        return 'ngày';\n      case 'monthly':\n        return 'tháng';\n      case 'quarterly':\n        return 'quý';\n      case 'yearly':\n        return 'năm';\n      default:\n        return 'khoảng thời gian';\n    }\n  };\n\n  // Get summary title\n  const getSummaryTitle = () => {\n    if (periodLabel) {\n      return `Tổng hợp doanh thu ${getPeriodTypeLabel()} ${periodLabel}`;\n    }\n    return `Tổng hợp doanh thu theo ${getPeriodTypeLabel()}`;\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 2,\n    sx: {\n      p: 2,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: getSummaryTitle()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            borderRadius: 1,\n            bgcolor: 'primary.light',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            color: \"primary.contrastText\",\n            children: \"T\\u1ED5ng doanh thu\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary.contrastText\",\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: formatCurrency(totalRevenue)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            borderRadius: 1,\n            bgcolor: 'secondary.light',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            color: \"secondary.contrastText\",\n            children: \"T\\u1ED5ng s\\u1ED1 h\\xF3a \\u0111\\u01A1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"secondary.contrastText\",\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: totalInvoices\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_c = StatisticsSummary;\nexport default StatisticsSummary;\nvar _c;\n$RefreshReg$(_c, \"StatisticsSummary\");", "map": {"version": 3, "names": ["React", "Box", "Paper", "Typography", "Grid", "Divider", "jsxDEV", "_jsxDEV", "StatisticsSummary", "data", "periodType", "period<PERSON><PERSON><PERSON>", "totalRevenue", "reduce", "sum", "item", "totalInvoices", "invoiceCount", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "maximumFractionDigits", "format", "getPeriodTypeLabel", "getSummaryTitle", "elevation", "sx", "p", "mb", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "xs", "sm", "borderRadius", "bgcolor", "width", "color", "fontWeight", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/statistics/StatisticsSummary.tsx"], "sourcesContent": ["import React from 'react';\nimport { Box, Paper, Typography, Grid, Divider } from '@mui/material';\nimport { TimeBasedRevenue } from '../../models';\n\ninterface StatisticsSummaryProps {\n  data: TimeBasedRevenue[];\n  periodType: string;\n  periodLabel?: string;\n}\n\nconst StatisticsSummary: React.FC<StatisticsSummaryProps> = ({\n  data,\n  periodType,\n  periodLabel\n}) => {\n  // Calculate total revenue\n  const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);\n\n  // Calculate total invoices\n  const totalInvoices = data.reduce((sum, item) => sum + item.invoiceCount, 0);\n\n  // Format currency\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('vi-VN', {\n      style: 'currency',\n      currency: 'VND',\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  // Get period type label\n  const getPeriodTypeLabel = (): string => {\n    switch (periodType) {\n      case 'daily':\n        return 'ngày';\n      case 'monthly':\n        return 'tháng';\n      case 'quarterly':\n        return 'quý';\n      case 'yearly':\n        return 'năm';\n      default:\n        return 'khoảng thời gian';\n    }\n  };\n\n  // Get summary title\n  const getSummaryTitle = (): string => {\n    if (periodLabel) {\n      return `Tổng hợp doanh thu ${getPeriodTypeLabel()} ${periodLabel}`;\n    }\n    return `Tổng hợp doanh thu theo ${getPeriodTypeLabel()}`;\n  };\n\n  return (\n    <Paper elevation={2} sx={{ p: 2, mb: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        {getSummaryTitle()}\n      </Typography>\n      <Divider sx={{ mb: 2 }} />\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} sm={6}>\n          <Box sx={{ p: 2, borderRadius: 1, bgcolor: 'primary.light', width: '100%' }}>\n            <Typography variant=\"subtitle2\" color=\"primary.contrastText\">\n              Tổng doanh thu\n            </Typography>\n            <Typography variant=\"h4\" color=\"primary.contrastText\" sx={{ fontWeight: 'bold' }}>\n              {formatCurrency(totalRevenue)}\n            </Typography>\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6}>\n          <Box sx={{ p: 2, borderRadius: 1, bgcolor: 'secondary.light', width: '100%' }}>\n            <Typography variant=\"subtitle2\" color=\"secondary.contrastText\">\n              Tổng số hóa đơn\n            </Typography>\n            <Typography variant=\"h4\" color=\"secondary.contrastText\" sx={{ fontWeight: 'bold' }}>\n              {totalInvoices}\n            </Typography>\n          </Box>\n        </Grid>\n      </Grid>\n    </Paper>\n  );\n};\n\nexport default StatisticsSummary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,IAAI,EAAEC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAStE,MAAMC,iBAAmD,GAAGA,CAAC;EAC3DC,IAAI;EACJC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ;EACA,MAAMC,YAAY,GAAGH,IAAI,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACH,YAAY,EAAE,CAAC,CAAC;;EAE3E;EACA,MAAMI,aAAa,GAAGP,IAAI,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACE,YAAY,EAAE,CAAC,CAAC;;EAE5E;EACA,MAAMC,cAAc,GAAIC,KAAa,IAAa;IAChD,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACN,KAAK,CAAC;EAClB,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAGA,CAAA,KAAc;IACvC,QAAQhB,UAAU;MAChB,KAAK,OAAO;QACV,OAAO,MAAM;MACf,KAAK,SAAS;QACZ,OAAO,OAAO;MAChB,KAAK,WAAW;QACd,OAAO,KAAK;MACd,KAAK,QAAQ;QACX,OAAO,KAAK;MACd;QACE,OAAO,kBAAkB;IAC7B;EACF,CAAC;;EAED;EACA,MAAMiB,eAAe,GAAGA,CAAA,KAAc;IACpC,IAAIhB,WAAW,EAAE;MACf,OAAO,sBAAsBe,kBAAkB,CAAC,CAAC,IAAIf,WAAW,EAAE;IACpE;IACA,OAAO,2BAA2Be,kBAAkB,CAAC,CAAC,EAAE;EAC1D,CAAC;EAED,oBACEnB,OAAA,CAACL,KAAK;IAAC0B,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACvCzB,OAAA,CAACJ,UAAU;MAAC8B,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAClCL,eAAe,CAAC;IAAC;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACb/B,OAAA,CAACF,OAAO;MAACwB,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1B/B,OAAA,CAACH,IAAI;MAACmC,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAR,QAAA,gBACzBzB,OAAA,CAACH,IAAI;QAACW,IAAI;QAAC0B,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACvBzB,OAAA,CAACN,GAAG;UAAC4B,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEa,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE,eAAe;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAb,QAAA,gBAC1EzB,OAAA,CAACJ,UAAU;YAAC8B,OAAO,EAAC,WAAW;YAACa,KAAK,EAAC,sBAAsB;YAAAd,QAAA,EAAC;UAE7D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/B,OAAA,CAACJ,UAAU;YAAC8B,OAAO,EAAC,IAAI;YAACa,KAAK,EAAC,sBAAsB;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAO,CAAE;YAAAf,QAAA,EAC9Ed,cAAc,CAACN,YAAY;UAAC;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEP/B,OAAA,CAACH,IAAI;QAACW,IAAI;QAAC0B,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACvBzB,OAAA,CAACN,GAAG;UAAC4B,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEa,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE,iBAAiB;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAb,QAAA,gBAC5EzB,OAAA,CAACJ,UAAU;YAAC8B,OAAO,EAAC,WAAW;YAACa,KAAK,EAAC,wBAAwB;YAAAd,QAAA,EAAC;UAE/D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/B,OAAA,CAACJ,UAAU;YAAC8B,OAAO,EAAC,IAAI;YAACa,KAAK,EAAC,wBAAwB;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAO,CAAE;YAAAf,QAAA,EAChFhB;UAAa;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ,CAAC;AAACU,EAAA,GA5EIxC,iBAAmD;AA8EzD,eAAeA,iBAAiB;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}