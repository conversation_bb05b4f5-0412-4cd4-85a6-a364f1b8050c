{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\pages\\\\CustomerPaymentPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Alert, Snackbar, Paper, Tabs, Tab, Divider, Button, useTheme, useMediaQuery } from '@mui/material';\nimport PersonIcon from '@mui/icons-material/Person';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport { PageHeader, LoadingSpinner, ErrorAlert } from '../components/common';\nimport { CustomerList, CustomerContractList, PaymentForm, SuccessNotification } from '../components/payment';\nimport { customerPaymentService, customerService } from '../services';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CustomerPaymentPage = () => {\n  _s();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n\n  // State for tabs\n  const [tabValue, setTabValue] = useState(0);\n\n  // State for customers\n  const [customers, setCustomers] = useState([]);\n  const [selectedCustomer, setSelectedCustomer] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // State for contracts\n  const [contracts, setContracts] = useState([]);\n  const [selectedContract, setSelectedContract] = useState(null);\n  const [remainingAmount, setRemainingAmount] = useState(0);\n\n  // State for payment form\n  const [paymentFormOpen, setPaymentFormOpen] = useState(false);\n\n  // State for UI\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [successMessage, setSuccessMessage] = useState(null);\n  const [showSuccessNotification, setShowSuccessNotification] = useState(false);\n\n  // Load customers on initial render\n  useEffect(() => {\n    fetchCustomers();\n  }, []);\n\n  // Fetch all customers\n  const fetchCustomers = async () => {\n    setLoading(true);\n    try {\n      const result = await customerService.getAllCustomers();\n      setCustomers(result);\n    } catch (err) {\n      console.error('Error fetching customers:', err);\n      setError('Đã xảy ra lỗi khi tải danh sách khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle tab change\n  const handleTabChange = (_event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  // Handle customer search\n  const handleSearch = async term => {\n    setSearchTerm(term);\n    if (!term.trim()) return;\n    setLoading(true);\n    try {\n      // Tìm kiếm theo cả tên và số điện thoại\n      const result = await customerPaymentService.searchCustomers(term, term);\n      setCustomers(result);\n      if (result.length === 0) {\n        setError('Không tìm thấy khách hàng nào phù hợp');\n      } else {\n        setError(null);\n      }\n    } catch (err) {\n      console.error('Error searching customers:', err);\n      setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle customer selection\n  const handleSelectCustomer = async customer => {\n    setSelectedCustomer(customer);\n    setTabValue(1); // Switch to contracts tab\n    setLoading(true);\n    setError(null);\n    try {\n      const activeContracts = await customerPaymentService.getActiveContractsByCustomerId(customer.id);\n      setContracts(activeContracts);\n    } catch (err) {\n      console.error('Error fetching contracts:', err);\n      setError('Đã xảy ra lỗi khi tải danh sách hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle payment button click\n  const handlePaymentClick = async contract => {\n    setSelectedContract(contract);\n    setLoading(true);\n    try {\n      // Get the latest contract payment info\n      const contractInfo = await customerPaymentService.getContractPaymentInfo(contract.id);\n      const remaining = await customerPaymentService.getRemainingAmountByContractId(contract.id);\n      setSelectedContract(contractInfo);\n      setRemainingAmount(remaining);\n      setPaymentFormOpen(true);\n    } catch (err) {\n      console.error('Error fetching contract payment info:', err);\n      setError('Đã xảy ra lỗi khi tải thông tin thanh toán hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle payment form close\n  const handlePaymentFormClose = () => {\n    setPaymentFormOpen(false);\n  };\n\n  // Handle payment form submit\n  const handlePaymentSubmit = async payment => {\n    // Prevent double submission with multiple checks\n    if (loading) {\n      console.log('Payment submission blocked: already loading');\n      return;\n    }\n\n    // Enhanced duplicate prevention\n    const now = Date.now();\n    const lastSubmission = localStorage.getItem('lastPaymentSubmission');\n    const submissionKey = `payment_${payment.customerContractId}_${payment.paymentAmount}_${payment.paymentMethod}`;\n    const lastSubmissionKey = localStorage.getItem('lastPaymentSubmissionKey');\n\n    // Prevent rapid successive submissions\n    if (lastSubmission && now - parseInt(lastSubmission) < 2000) {\n      console.log('Payment submission blocked: too rapid (within 2 seconds)');\n      setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');\n      return;\n    }\n\n    // Prevent duplicate payment submissions\n    if (lastSubmissionKey === submissionKey && lastSubmission && now - parseInt(lastSubmission) < 60000) {\n      console.log('Payment submission blocked: duplicate payment detected');\n      setError('Thanh toán tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');\n      return;\n    }\n\n    // Mark submission time and key to prevent rapid resubmission and duplicates\n    localStorage.setItem('lastPaymentSubmission', now.toString());\n    localStorage.setItem('lastPaymentSubmissionKey', submissionKey);\n    setLoading(true);\n    setError(null);\n    try {\n      var _createdPayment$payme;\n      console.log('🚀 Submitting payment creation request...', payment);\n\n      // Clear any previous error state\n      setError(null);\n      const createdPayment = await customerPaymentService.createPayment(payment);\n      console.log('✅ Payment created successfully:', {\n        id: createdPayment.id,\n        amount: createdPayment.paymentAmount,\n        contractId: createdPayment.customerContractId\n      });\n\n      // Verify the payment was actually created with valid data\n      if (!createdPayment || !createdPayment.id) {\n        throw new Error('Thanh toán được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');\n      }\n      setSuccessMessage(`Thanh toán #${createdPayment.id} thành công với số tiền ${(_createdPayment$payme = createdPayment.paymentAmount) === null || _createdPayment$payme === void 0 ? void 0 : _createdPayment$payme.toLocaleString('vi-VN')} VNĐ!`);\n      setPaymentFormOpen(false);\n      setShowSuccessNotification(true);\n\n      // Clear the submission timestamp and key on success\n      localStorage.removeItem('lastPaymentSubmission');\n      localStorage.removeItem('lastPaymentSubmissionKey');\n\n      // Refresh contracts list to show updated payment information\n      if (selectedCustomer) {\n        try {\n          const activeContracts = await customerPaymentService.getActiveContractsByCustomerId(selectedCustomer.id);\n          setContracts(activeContracts);\n          console.log('✅ Contracts list refreshed after payment');\n        } catch (refreshError) {\n          console.warn('⚠️ Failed to refresh contracts list:', refreshError);\n          // Don't show error for refresh failure, payment was successful\n        }\n      }\n\n      // Set flag to trigger refresh in contracts list page\n      localStorage.setItem('contractsListNeedsRefresh', 'true');\n    } catch (err) {\n      var _err$response, _err$response2;\n      console.error('❌ Payment creation failed:', err);\n\n      // Provide more specific error messages\n      let errorMessage = 'Đã xảy ra lỗi khi tạo thanh toán';\n      if (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 400) {\n        errorMessage = 'Dữ liệu thanh toán không hợp lệ. Vui lòng kiểm tra lại thông tin.';\n      } else if (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 500) {\n        errorMessage = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n      setError(errorMessage);\n\n      // Clear the submission timestamp on error to allow retry\n      localStorage.removeItem('lastPaymentSubmission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle success message close\n  const handleSuccessClose = () => {\n    setSuccessMessage(null);\n  };\n\n  // Handle success notification close\n  const handleSuccessNotificationClose = () => {\n    setShowSuccessNotification(false);\n  };\n\n  // Handle back to customer list\n  const handleBackToCustomers = () => {\n    setTabValue(0);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      title: \"Thanh to\\xE1n h\\u1EE3p \\u0111\\u1ED3ng kh\\xE1ch h\\xE0ng\",\n      subtitle: \"Qu\\u1EA3n l\\xFD thanh to\\xE1n h\\u1EE3p \\u0111\\u1ED3ng kh\\xE1ch h\\xE0ng thu\\xEA lao \\u0111\\u1ED9ng\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Tabs, {\n        value: tabValue,\n        onChange: handleTabChange,\n        variant: isMobile ? \"fullWidth\" : \"standard\",\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 19\n          }, this),\n          label: \"Danh s\\xE1ch kh\\xE1ch h\\xE0ng\",\n          iconPosition: \"start\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), selectedCustomer && /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 21\n          }, this),\n          label: \"H\\u1EE3p \\u0111\\u1ED3ng c\\u1EA7n thanh to\\xE1n\",\n          iconPosition: \"start\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3\n        },\n        children: [tabValue === 0 && /*#__PURE__*/_jsxDEV(CustomerList, {\n          customers: customers,\n          onSelectCustomer: handleSelectCustomer,\n          onSearch: handleSearch,\n          searchTerm: searchTerm,\n          setSearchTerm: setSearchTerm,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), tabValue === 1 && selectedCustomer && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"Kh\\xE1ch h\\xE0ng: \", selectedCustomer.fullName, selectedCustomer.companyName && ` (${selectedCustomer.companyName})`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 30\n              }, this),\n              onClick: handleBackToCustomers,\n              children: \"Quay l\\u1EA1i danh s\\xE1ch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), loading ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(CustomerContractList, {\n            contracts: contracts,\n            onPaymentClick: handlePaymentClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PaymentForm, {\n      open: paymentFormOpen,\n      contract: selectedContract,\n      onClose: handlePaymentFormClose,\n      onSubmit: handlePaymentSubmit,\n      remainingAmount: remainingAmount,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SuccessNotification, {\n      open: showSuccessNotification,\n      message: \"Thanh to\\xE1n \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c ghi nh\\u1EADn th\\xE0nh c\\xF4ng!\",\n      onClose: handleSuccessNotificationClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: !!successMessage,\n      autoHideDuration: 6000,\n      onClose: handleSuccessClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleSuccessClose,\n        severity: \"success\",\n        sx: {\n          width: '100%'\n        },\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n_s(CustomerPaymentPage, \"eP//BP0PmrplknmHa/3cnYlYhZ0=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = CustomerPaymentPage;\nexport default CustomerPaymentPage;\nvar _c;\n$RefreshReg$(_c, \"CustomerPaymentPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Snackbar", "Paper", "Tabs", "Tab", "Divider", "<PERSON><PERSON>", "useTheme", "useMediaQuery", "PersonIcon", "PaymentIcon", "<PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CustomerList", "CustomerContractList", "PaymentForm", "SuccessNotification", "customerPaymentService", "customerService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomerPaymentPage", "_s", "theme", "isMobile", "breakpoints", "down", "tabValue", "setTabValue", "customers", "setCustomers", "selectedCustomer", "setSelectedCustomer", "searchTerm", "setSearchTerm", "contracts", "setContracts", "selectedContract", "setSelectedContract", "remainingAmount", "setRemainingAmount", "paymentFormOpen", "setPaymentFormOpen", "loading", "setLoading", "error", "setError", "successMessage", "setSuccessMessage", "showSuccessNotification", "setShowSuccessNotification", "fetchCustomers", "result", "getAllCustomers", "err", "console", "handleTabChange", "_event", "newValue", "handleSearch", "term", "trim", "searchCustomers", "length", "handleSelectCustomer", "customer", "activeContracts", "getActiveContractsByCustomerId", "id", "handlePaymentClick", "contract", "contractInfo", "getContractPaymentInfo", "remaining", "getRemainingAmountByContractId", "handlePaymentFormClose", "handlePaymentSubmit", "payment", "log", "now", "Date", "lastSubmission", "localStorage", "getItem", "<PERSON><PERSON><PERSON>", "customerContractId", "paymentAmount", "paymentMethod", "lastSubmissionKey", "parseInt", "setItem", "toString", "_createdPayment$payme", "createdPayment", "createPayment", "amount", "contractId", "Error", "toLocaleString", "removeItem", "refreshError", "warn", "_err$response", "_err$response2", "errorMessage", "response", "status", "message", "handleSuccessClose", "handleSuccessNotificationClose", "handleBackToCustomers", "children", "title", "subtitle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "value", "onChange", "variant", "borderBottom", "borderColor", "icon", "label", "iconPosition", "p", "onSelectCustomer", "onSearch", "display", "justifyContent", "alignItems", "fullName", "companyName", "startIcon", "onClick", "onPaymentClick", "open", "onClose", "onSubmit", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/pages/CustomerPaymentPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Alert,\n  Snackbar,\n  Paper,\n  Tabs,\n  Tab,\n  Divider,\n  Button,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\n\nimport PersonIcon from '@mui/icons-material/Person';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport { Page<PERSON>eader, LoadingSpinner, ErrorAlert } from '../components/common';\nimport {\n  CustomerList,\n  CustomerContractList,\n  PaymentForm,\n  SuccessNotification\n} from '../components/payment';\nimport { customerPaymentService, customerService } from '../services';\nimport { Customer, CustomerContract, CustomerPayment } from '../models';\n\nconst CustomerPaymentPage: React.FC = () => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n\n  // State for tabs\n  const [tabValue, setTabValue] = useState<number>(0);\n\n  // State for customers\n  const [customers, setCustomers] = useState<Customer[]>([]);\n  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);\n  const [searchTerm, setSearchTerm] = useState<string>('');\n\n  // State for contracts\n  const [contracts, setContracts] = useState<CustomerContract[]>([]);\n  const [selectedContract, setSelectedContract] = useState<CustomerContract | null>(null);\n  const [remainingAmount, setRemainingAmount] = useState<number>(0);\n\n  // State for payment form\n  const [paymentFormOpen, setPaymentFormOpen] = useState<boolean>(false);\n\n  // State for UI\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [showSuccessNotification, setShowSuccessNotification] = useState<boolean>(false);\n\n  // Load customers on initial render\n  useEffect(() => {\n    fetchCustomers();\n  }, []);\n\n  // Fetch all customers\n  const fetchCustomers = async () => {\n    setLoading(true);\n    try {\n      const result = await customerService.getAllCustomers();\n      setCustomers(result);\n    } catch (err) {\n      console.error('Error fetching customers:', err);\n      setError('Đã xảy ra lỗi khi tải danh sách khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle tab change\n  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {\n    setTabValue(newValue);\n  };\n\n  // Handle customer search\n  const handleSearch = async (term: string) => {\n    setSearchTerm(term);\n    if (!term.trim()) return;\n\n    setLoading(true);\n    try {\n      // Tìm kiếm theo cả tên và số điện thoại\n      const result = await customerPaymentService.searchCustomers(term, term);\n      setCustomers(result);\n\n      if (result.length === 0) {\n        setError('Không tìm thấy khách hàng nào phù hợp');\n      } else {\n        setError(null);\n      }\n    } catch (err) {\n      console.error('Error searching customers:', err);\n      setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle customer selection\n  const handleSelectCustomer = async (customer: Customer) => {\n    setSelectedCustomer(customer);\n    setTabValue(1); // Switch to contracts tab\n    setLoading(true);\n    setError(null);\n\n    try {\n      const activeContracts = await customerPaymentService.getActiveContractsByCustomerId(customer.id!);\n      setContracts(activeContracts);\n    } catch (err) {\n      console.error('Error fetching contracts:', err);\n      setError('Đã xảy ra lỗi khi tải danh sách hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle payment button click\n  const handlePaymentClick = async (contract: CustomerContract) => {\n    setSelectedContract(contract);\n    setLoading(true);\n\n    try {\n      // Get the latest contract payment info\n      const contractInfo = await customerPaymentService.getContractPaymentInfo(contract.id!);\n      const remaining = await customerPaymentService.getRemainingAmountByContractId(contract.id!);\n\n      setSelectedContract(contractInfo);\n      setRemainingAmount(remaining);\n      setPaymentFormOpen(true);\n    } catch (err) {\n      console.error('Error fetching contract payment info:', err);\n      setError('Đã xảy ra lỗi khi tải thông tin thanh toán hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle payment form close\n  const handlePaymentFormClose = () => {\n    setPaymentFormOpen(false);\n  };\n\n  // Handle payment form submit\n  const handlePaymentSubmit = async (payment: CustomerPayment) => {\n    // Prevent double submission with multiple checks\n    if (loading) {\n      console.log('Payment submission blocked: already loading');\n      return;\n    }\n\n    // Enhanced duplicate prevention\n    const now = Date.now();\n    const lastSubmission = localStorage.getItem('lastPaymentSubmission');\n    const submissionKey = `payment_${payment.customerContractId}_${payment.paymentAmount}_${payment.paymentMethod}`;\n    const lastSubmissionKey = localStorage.getItem('lastPaymentSubmissionKey');\n\n    // Prevent rapid successive submissions\n    if (lastSubmission && (now - parseInt(lastSubmission)) < 2000) {\n      console.log('Payment submission blocked: too rapid (within 2 seconds)');\n      setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');\n      return;\n    }\n\n    // Prevent duplicate payment submissions\n    if (lastSubmissionKey === submissionKey && lastSubmission && (now - parseInt(lastSubmission)) < 60000) {\n      console.log('Payment submission blocked: duplicate payment detected');\n      setError('Thanh toán tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');\n      return;\n    }\n\n    // Mark submission time and key to prevent rapid resubmission and duplicates\n    localStorage.setItem('lastPaymentSubmission', now.toString());\n    localStorage.setItem('lastPaymentSubmissionKey', submissionKey);\n    setLoading(true);\n    setError(null);\n\n    try {\n      console.log('🚀 Submitting payment creation request...', payment);\n\n      // Clear any previous error state\n      setError(null);\n\n      const createdPayment = await customerPaymentService.createPayment(payment);\n      console.log('✅ Payment created successfully:', {\n        id: createdPayment.id,\n        amount: createdPayment.paymentAmount,\n        contractId: createdPayment.customerContractId\n      });\n\n      // Verify the payment was actually created with valid data\n      if (!createdPayment || !createdPayment.id) {\n        throw new Error('Thanh toán được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');\n      }\n\n      setSuccessMessage(`Thanh toán #${createdPayment.id} thành công với số tiền ${createdPayment.paymentAmount?.toLocaleString('vi-VN')} VNĐ!`);\n      setPaymentFormOpen(false);\n      setShowSuccessNotification(true);\n\n      // Clear the submission timestamp and key on success\n      localStorage.removeItem('lastPaymentSubmission');\n      localStorage.removeItem('lastPaymentSubmissionKey');\n\n      // Refresh contracts list to show updated payment information\n      if (selectedCustomer) {\n        try {\n          const activeContracts = await customerPaymentService.getActiveContractsByCustomerId(selectedCustomer.id!);\n          setContracts(activeContracts);\n          console.log('✅ Contracts list refreshed after payment');\n        } catch (refreshError) {\n          console.warn('⚠️ Failed to refresh contracts list:', refreshError);\n          // Don't show error for refresh failure, payment was successful\n        }\n      }\n\n      // Set flag to trigger refresh in contracts list page\n      localStorage.setItem('contractsListNeedsRefresh', 'true');\n    } catch (err: any) {\n      console.error('❌ Payment creation failed:', err);\n\n      // Provide more specific error messages\n      let errorMessage = 'Đã xảy ra lỗi khi tạo thanh toán';\n\n      if (err.response?.status === 400) {\n        errorMessage = 'Dữ liệu thanh toán không hợp lệ. Vui lòng kiểm tra lại thông tin.';\n      } else if (err.response?.status === 500) {\n        errorMessage = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n\n      setError(errorMessage);\n\n      // Clear the submission timestamp on error to allow retry\n      localStorage.removeItem('lastPaymentSubmission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle success message close\n  const handleSuccessClose = () => {\n    setSuccessMessage(null);\n  };\n\n  // Handle success notification close\n  const handleSuccessNotificationClose = () => {\n    setShowSuccessNotification(false);\n  };\n\n  // Handle back to customer list\n  const handleBackToCustomers = () => {\n    setTabValue(0);\n  };\n\n  return (\n    <Box>\n      <PageHeader\n        title=\"Thanh toán hợp đồng khách hàng\"\n        subtitle=\"Quản lý thanh toán hợp đồng khách hàng thuê lao động\"\n      />\n\n      {error && <ErrorAlert message={error} />}\n\n      <Paper sx={{ mb: 3 }}>\n        <Tabs\n          value={tabValue}\n          onChange={handleTabChange}\n          variant={isMobile ? \"fullWidth\" : \"standard\"}\n          sx={{ borderBottom: 1, borderColor: 'divider' }}\n        >\n          <Tab\n            icon={<PersonIcon />}\n            label=\"Danh sách khách hàng\"\n            iconPosition=\"start\"\n          />\n          {selectedCustomer && (\n            <Tab\n              icon={<PaymentIcon />}\n              label=\"Hợp đồng cần thanh toán\"\n              iconPosition=\"start\"\n            />\n          )}\n        </Tabs>\n\n        <Box sx={{ p: 3 }}>\n          {tabValue === 0 && (\n            <CustomerList\n              customers={customers}\n              onSelectCustomer={handleSelectCustomer}\n              onSearch={handleSearch}\n              searchTerm={searchTerm}\n              setSearchTerm={setSearchTerm}\n              loading={loading}\n            />\n          )}\n\n          {tabValue === 1 && selectedCustomer && (\n            <>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                <Typography variant=\"h6\">\n                  Khách hàng: {selectedCustomer.fullName}\n                  {selectedCustomer.companyName && ` (${selectedCustomer.companyName})`}\n                </Typography>\n                <Button\n                  variant=\"outlined\"\n                  startIcon={<PersonIcon />}\n                  onClick={handleBackToCustomers}\n                >\n                  Quay lại danh sách\n                </Button>\n              </Box>\n              <Divider sx={{ mb: 2 }} />\n\n              {loading ? (\n                <LoadingSpinner />\n              ) : (\n                <CustomerContractList\n                  contracts={contracts}\n                  onPaymentClick={handlePaymentClick}\n                />\n              )}\n            </>\n          )}\n        </Box>\n      </Paper>\n\n      <PaymentForm\n        open={paymentFormOpen}\n        contract={selectedContract}\n        onClose={handlePaymentFormClose}\n        onSubmit={handlePaymentSubmit}\n        remainingAmount={remainingAmount}\n        loading={loading}\n      />\n\n      <SuccessNotification\n        open={showSuccessNotification}\n        message=\"Thanh toán đã được ghi nhận thành công!\"\n        onClose={handleSuccessNotificationClose}\n      />\n\n      <Snackbar\n        open={!!successMessage}\n        autoHideDuration={6000}\n        onClose={handleSuccessClose}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleSuccessClose} severity=\"success\" sx={{ width: '100%' }}>\n          {successMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CustomerPaymentPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,aAAa,QACR,eAAe;AAEtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,UAAU,EAAEC,cAAc,EAAEC,UAAU,QAAQ,sBAAsB;AAC7E,SACEC,YAAY,EACZC,oBAAoB,EACpBC,WAAW,EACXC,mBAAmB,QACd,uBAAuB;AAC9B,SAASC,sBAAsB,EAAEC,eAAe,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGtE,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAMC,KAAK,GAAGnB,QAAQ,CAAC,CAAC;EACxB,MAAMoB,QAAQ,GAAGnB,aAAa,CAACkB,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAS,CAAC,CAAC;;EAEnD;EACA,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACsC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAkB,IAAI,CAAC;EAC/E,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAS,EAAE,CAAC;;EAExD;EACA,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAqB,EAAE,CAAC;EAClE,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAS,CAAC,CAAC;;EAEjE;EACA,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAU,KAAK,CAAC;;EAEtE;EACA,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACsD,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACwD,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGzD,QAAQ,CAAU,KAAK,CAAC;;EAEtF;EACAC,SAAS,CAAC,MAAM;IACdyD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,MAAM,GAAG,MAAMpC,eAAe,CAACqC,eAAe,CAAC,CAAC;MACtDvB,YAAY,CAACsB,MAAM,CAAC;IACtB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACV,KAAK,CAAC,2BAA2B,EAAES,GAAG,CAAC;MAC/CR,QAAQ,CAAC,4CAA4C,CAAC;IACxD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMY,eAAe,GAAGA,CAACC,MAA4B,EAAEC,QAAgB,KAAK;IAC1E9B,WAAW,CAAC8B,QAAQ,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAOC,IAAY,IAAK;IAC3C1B,aAAa,CAAC0B,IAAI,CAAC;IACnB,IAAI,CAACA,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE;IAElBjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMQ,MAAM,GAAG,MAAMrC,sBAAsB,CAAC+C,eAAe,CAACF,IAAI,EAAEA,IAAI,CAAC;MACvE9B,YAAY,CAACsB,MAAM,CAAC;MAEpB,IAAIA,MAAM,CAACW,MAAM,KAAK,CAAC,EAAE;QACvBjB,QAAQ,CAAC,uCAAuC,CAAC;MACnD,CAAC,MAAM;QACLA,QAAQ,CAAC,IAAI,CAAC;MAChB;IACF,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZC,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAES,GAAG,CAAC;MAChDR,QAAQ,CAAC,uCAAuC,CAAC;IACnD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoB,oBAAoB,GAAG,MAAOC,QAAkB,IAAK;IACzDjC,mBAAmB,CAACiC,QAAQ,CAAC;IAC7BrC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAChBgB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMoB,eAAe,GAAG,MAAMnD,sBAAsB,CAACoD,8BAA8B,CAACF,QAAQ,CAACG,EAAG,CAAC;MACjGhC,YAAY,CAAC8B,eAAe,CAAC;IAC/B,CAAC,CAAC,OAAOZ,GAAG,EAAE;MACZC,OAAO,CAACV,KAAK,CAAC,2BAA2B,EAAES,GAAG,CAAC;MAC/CR,QAAQ,CAAC,0CAA0C,CAAC;IACtD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyB,kBAAkB,GAAG,MAAOC,QAA0B,IAAK;IAC/DhC,mBAAmB,CAACgC,QAAQ,CAAC;IAC7B1B,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM2B,YAAY,GAAG,MAAMxD,sBAAsB,CAACyD,sBAAsB,CAACF,QAAQ,CAACF,EAAG,CAAC;MACtF,MAAMK,SAAS,GAAG,MAAM1D,sBAAsB,CAAC2D,8BAA8B,CAACJ,QAAQ,CAACF,EAAG,CAAC;MAE3F9B,mBAAmB,CAACiC,YAAY,CAAC;MACjC/B,kBAAkB,CAACiC,SAAS,CAAC;MAC7B/B,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOY,GAAG,EAAE;MACZC,OAAO,CAACV,KAAK,CAAC,uCAAuC,EAAES,GAAG,CAAC;MAC3DR,QAAQ,CAAC,qDAAqD,CAAC;IACjE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+B,sBAAsB,GAAGA,CAAA,KAAM;IACnCjC,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMkC,mBAAmB,GAAG,MAAOC,OAAwB,IAAK;IAC9D;IACA,IAAIlC,OAAO,EAAE;MACXY,OAAO,CAACuB,GAAG,CAAC,6CAA6C,CAAC;MAC1D;IACF;;IAEA;IACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IACpE,MAAMC,aAAa,GAAG,WAAWP,OAAO,CAACQ,kBAAkB,IAAIR,OAAO,CAACS,aAAa,IAAIT,OAAO,CAACU,aAAa,EAAE;IAC/G,MAAMC,iBAAiB,GAAGN,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC;;IAE1E;IACA,IAAIF,cAAc,IAAKF,GAAG,GAAGU,QAAQ,CAACR,cAAc,CAAC,GAAI,IAAI,EAAE;MAC7D1B,OAAO,CAACuB,GAAG,CAAC,0DAA0D,CAAC;MACvEhC,QAAQ,CAAC,+CAA+C,CAAC;MACzD;IACF;;IAEA;IACA,IAAI0C,iBAAiB,KAAKJ,aAAa,IAAIH,cAAc,IAAKF,GAAG,GAAGU,QAAQ,CAACR,cAAc,CAAC,GAAI,KAAK,EAAE;MACrG1B,OAAO,CAACuB,GAAG,CAAC,wDAAwD,CAAC;MACrEhC,QAAQ,CAAC,iEAAiE,CAAC;MAC3E;IACF;;IAEA;IACAoC,YAAY,CAACQ,OAAO,CAAC,uBAAuB,EAAEX,GAAG,CAACY,QAAQ,CAAC,CAAC,CAAC;IAC7DT,YAAY,CAACQ,OAAO,CAAC,0BAA0B,EAAEN,aAAa,CAAC;IAC/DxC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAAA,IAAA8C,qBAAA;MACFrC,OAAO,CAACuB,GAAG,CAAC,2CAA2C,EAAED,OAAO,CAAC;;MAEjE;MACA/B,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM+C,cAAc,GAAG,MAAM9E,sBAAsB,CAAC+E,aAAa,CAACjB,OAAO,CAAC;MAC1EtB,OAAO,CAACuB,GAAG,CAAC,iCAAiC,EAAE;QAC7CV,EAAE,EAAEyB,cAAc,CAACzB,EAAE;QACrB2B,MAAM,EAAEF,cAAc,CAACP,aAAa;QACpCU,UAAU,EAAEH,cAAc,CAACR;MAC7B,CAAC,CAAC;;MAEF;MACA,IAAI,CAACQ,cAAc,IAAI,CAACA,cAAc,CAACzB,EAAE,EAAE;QACzC,MAAM,IAAI6B,KAAK,CAAC,uEAAuE,CAAC;MAC1F;MAEAjD,iBAAiB,CAAC,eAAe6C,cAAc,CAACzB,EAAE,4BAAAwB,qBAAA,GAA2BC,cAAc,CAACP,aAAa,cAAAM,qBAAA,uBAA5BA,qBAAA,CAA8BM,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC;MAC1IxD,kBAAkB,CAAC,KAAK,CAAC;MACzBQ,0BAA0B,CAAC,IAAI,CAAC;;MAEhC;MACAgC,YAAY,CAACiB,UAAU,CAAC,uBAAuB,CAAC;MAChDjB,YAAY,CAACiB,UAAU,CAAC,0BAA0B,CAAC;;MAEnD;MACA,IAAIpE,gBAAgB,EAAE;QACpB,IAAI;UACF,MAAMmC,eAAe,GAAG,MAAMnD,sBAAsB,CAACoD,8BAA8B,CAACpC,gBAAgB,CAACqC,EAAG,CAAC;UACzGhC,YAAY,CAAC8B,eAAe,CAAC;UAC7BX,OAAO,CAACuB,GAAG,CAAC,0CAA0C,CAAC;QACzD,CAAC,CAAC,OAAOsB,YAAY,EAAE;UACrB7C,OAAO,CAAC8C,IAAI,CAAC,sCAAsC,EAAED,YAAY,CAAC;UAClE;QACF;MACF;;MAEA;MACAlB,YAAY,CAACQ,OAAO,CAAC,2BAA2B,EAAE,MAAM,CAAC;IAC3D,CAAC,CAAC,OAAOpC,GAAQ,EAAE;MAAA,IAAAgD,aAAA,EAAAC,cAAA;MACjBhD,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAES,GAAG,CAAC;;MAEhD;MACA,IAAIkD,YAAY,GAAG,kCAAkC;MAErD,IAAI,EAAAF,aAAA,GAAAhD,GAAG,CAACmD,QAAQ,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,MAAM,MAAK,GAAG,EAAE;QAChCF,YAAY,GAAG,mEAAmE;MACpF,CAAC,MAAM,IAAI,EAAAD,cAAA,GAAAjD,GAAG,CAACmD,QAAQ,cAAAF,cAAA,uBAAZA,cAAA,CAAcG,MAAM,MAAK,GAAG,EAAE;QACvCF,YAAY,GAAG,2CAA2C;MAC5D,CAAC,MAAM,IAAIlD,GAAG,CAACqD,OAAO,EAAE;QACtBH,YAAY,GAAGlD,GAAG,CAACqD,OAAO;MAC5B;MAEA7D,QAAQ,CAAC0D,YAAY,CAAC;;MAEtB;MACAtB,YAAY,CAACiB,UAAU,CAAC,uBAAuB,CAAC;IAClD,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B5D,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM6D,8BAA8B,GAAGA,CAAA,KAAM;IAC3C3D,0BAA0B,CAAC,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAM4D,qBAAqB,GAAGA,CAAA,KAAM;IAClClF,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,oBACEV,OAAA,CAACvB,GAAG;IAAAoH,QAAA,gBACF7F,OAAA,CAACV,UAAU;MACTwG,KAAK,EAAC,wDAAgC;MACtCC,QAAQ,EAAC;IAAsD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,EAEDxE,KAAK,iBAAI3B,OAAA,CAACR,UAAU;MAACiG,OAAO,EAAE9D;IAAM;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAExCnG,OAAA,CAACnB,KAAK;MAACuH,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACnB7F,OAAA,CAAClB,IAAI;QACHwH,KAAK,EAAE7F,QAAS;QAChB8F,QAAQ,EAAEjE,eAAgB;QAC1BkE,OAAO,EAAElG,QAAQ,GAAG,WAAW,GAAG,UAAW;QAC7C8F,EAAE,EAAE;UAAEK,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAAb,QAAA,gBAEhD7F,OAAA,CAACjB,GAAG;UACF4H,IAAI,eAAE3G,OAAA,CAACZ,UAAU;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBS,KAAK,EAAC,+BAAsB;UAC5BC,YAAY,EAAC;QAAO;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,EACDtF,gBAAgB,iBACfb,OAAA,CAACjB,GAAG;UACF4H,IAAI,eAAE3G,OAAA,CAACX,WAAW;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBS,KAAK,EAAC,gDAAyB;UAC/BC,YAAY,EAAC;QAAO;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEPnG,OAAA,CAACvB,GAAG;QAAC2H,EAAE,EAAE;UAAEU,CAAC,EAAE;QAAE,CAAE;QAAAjB,QAAA,GACfpF,QAAQ,KAAK,CAAC,iBACbT,OAAA,CAACP,YAAY;UACXkB,SAAS,EAAEA,SAAU;UACrBoG,gBAAgB,EAAEjE,oBAAqB;UACvCkE,QAAQ,EAAEvE,YAAa;UACvB1B,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7BS,OAAO,EAAEA;QAAQ;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACF,EAEA1F,QAAQ,KAAK,CAAC,IAAII,gBAAgB,iBACjCb,OAAA,CAAAE,SAAA;UAAA2F,QAAA,gBACE7F,OAAA,CAACvB,GAAG;YAAC2H,EAAE,EAAE;cAAEa,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,QAAQ;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,gBACzF7F,OAAA,CAACtB,UAAU;cAAC8H,OAAO,EAAC,IAAI;cAAAX,QAAA,GAAC,oBACX,EAAChF,gBAAgB,CAACuG,QAAQ,EACrCvG,gBAAgB,CAACwG,WAAW,IAAI,KAAKxG,gBAAgB,CAACwG,WAAW,GAAG;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACbnG,OAAA,CAACf,MAAM;cACLuH,OAAO,EAAC,UAAU;cAClBc,SAAS,eAAEtH,OAAA,CAACZ,UAAU;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BoB,OAAO,EAAE3B,qBAAsB;cAAAC,QAAA,EAChC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNnG,OAAA,CAAChB,OAAO;YAACoH,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAEzB1E,OAAO,gBACNzB,OAAA,CAACT,cAAc;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAElBnG,OAAA,CAACN,oBAAoB;YACnBuB,SAAS,EAAEA,SAAU;YACrBuG,cAAc,EAAErE;UAAmB;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACF;QAAA,eACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAERnG,OAAA,CAACL,WAAW;MACV8H,IAAI,EAAElG,eAAgB;MACtB6B,QAAQ,EAAEjC,gBAAiB;MAC3BuG,OAAO,EAAEjE,sBAAuB;MAChCkE,QAAQ,EAAEjE,mBAAoB;MAC9BrC,eAAe,EAAEA,eAAgB;MACjCI,OAAO,EAAEA;IAAQ;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAEFnG,OAAA,CAACJ,mBAAmB;MAClB6H,IAAI,EAAE1F,uBAAwB;MAC9B0D,OAAO,EAAC,8EAAyC;MACjDiC,OAAO,EAAE/B;IAA+B;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC,eAEFnG,OAAA,CAACpB,QAAQ;MACP6I,IAAI,EAAE,CAAC,CAAC5F,cAAe;MACvB+F,gBAAgB,EAAE,IAAK;MACvBF,OAAO,EAAEhC,kBAAmB;MAC5BmC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAlC,QAAA,eAE3D7F,OAAA,CAACrB,KAAK;QAAC+I,OAAO,EAAEhC,kBAAmB;QAACsC,QAAQ,EAAC,SAAS;QAAC5B,EAAE,EAAE;UAAE6B,KAAK,EAAE;QAAO,CAAE;QAAApC,QAAA,EAC1EhE;MAAc;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC/F,EAAA,CAzUID,mBAA6B;EAAA,QACnBjB,QAAQ,EACLC,aAAa;AAAA;AAAA+I,EAAA,GAF1B/H,mBAA6B;AA2UnC,eAAeA,mBAAmB;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}