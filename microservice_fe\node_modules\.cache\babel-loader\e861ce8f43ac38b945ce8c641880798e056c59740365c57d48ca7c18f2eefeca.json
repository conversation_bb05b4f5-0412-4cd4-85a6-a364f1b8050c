{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Alert,AlertTitle,Box,IconButton,Collapse}from'@mui/material';import CloseIcon from'@mui/icons-material/Close';import RefreshIcon from'@mui/icons-material/Refresh';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ErrorAlert(_ref){let{title='Lỗi',message,onRetry,autoHide=false,hideAfter=10000// 10 seconds default\n}=_ref;const[open,setOpen]=useState(true);// Auto-hide functionality\nuseEffect(()=>{let timer;if(autoHide&&open){timer=setTimeout(()=>{setOpen(false);},hideAfter);}return()=>{if(timer)clearTimeout(timer);};},[autoHide,hideAfter,open]);// Format error message for better display\nconst formatErrorMessage=msg=>{// If message is too long, truncate it\nif(msg.length>200){return msg.substring(0,200)+'...';}return msg;};return/*#__PURE__*/_jsx(Box,{sx:{my:2},children:/*#__PURE__*/_jsx(Collapse,{in:open,children:/*#__PURE__*/_jsxs(Alert,{severity:\"error\",action:/*#__PURE__*/_jsxs(Box,{children:[onRetry&&/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"retry\",color:\"inherit\",size:\"small\",onClick:onRetry,title:\"Th\\u1EED l\\u1EA1i\",children:/*#__PURE__*/_jsx(RefreshIcon,{fontSize:\"inherit\"})}),/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",color:\"inherit\",size:\"small\",onClick:()=>setOpen(false),children:/*#__PURE__*/_jsx(CloseIcon,{fontSize:\"inherit\"})})]}),sx:{'& .MuiAlert-message':{wordBreak:'break-word',whiteSpace:'pre-wrap'}},children:[/*#__PURE__*/_jsx(AlertTitle,{children:title}),formatErrorMessage(message)]})})});};export default ErrorAlert;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "IconButton", "Collapse", "CloseIcon", "RefreshIcon", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "title", "message", "onRetry", "autoHide", "hideAfter", "open", "<PERSON><PERSON><PERSON>", "timer", "setTimeout", "clearTimeout", "formatErrorMessage", "msg", "length", "substring", "sx", "my", "children", "in", "severity", "action", "color", "size", "onClick", "fontSize", "wordBreak", "whiteSpace"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/common/ErrorAlert.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Alert, AlertTitle, Box, IconButton, Collapse } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport RefreshIcon from '@mui/icons-material/Refresh';\n\ninterface ErrorAlertProps {\n  title?: string;\n  message: string;\n  onRetry?: () => void;\n  autoHide?: boolean;\n  hideAfter?: number; // in milliseconds\n}\n\nfunction ErrorAlert({\n  title = 'Lỗi',\n  message,\n  onRetry,\n  autoHide = false,\n  hideAfter = 10000 // 10 seconds default\n}: ErrorAlertProps) {\n  const [open, setOpen] = useState(true);\n\n  // Auto-hide functionality\n  useEffect(() => {\n    let timer: NodeJS.Timeout;\n    if (autoHide && open) {\n      timer = setTimeout(() => {\n        setOpen(false);\n      }, hideAfter);\n    }\n\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [autoHide, hideAfter, open]);\n\n  // Format error message for better display\n  const formatErrorMessage = (msg: string) => {\n    // If message is too long, truncate it\n    if (msg.length > 200) {\n      return msg.substring(0, 200) + '...';\n    }\n    return msg;\n  };\n\n  return (\n    <Box sx={{ my: 2 }}>\n      <Collapse in={open}>\n        <Alert\n          severity=\"error\"\n          action={\n            <Box>\n              {onRetry && (\n                <IconButton\n                  aria-label=\"retry\"\n                  color=\"inherit\"\n                  size=\"small\"\n                  onClick={onRetry}\n                  title=\"Thử lại\"\n                >\n                  <RefreshIcon fontSize=\"inherit\" />\n                </IconButton>\n              )}\n              <IconButton\n                aria-label=\"close\"\n                color=\"inherit\"\n                size=\"small\"\n                onClick={() => setOpen(false)}\n              >\n                <CloseIcon fontSize=\"inherit\" />\n              </IconButton>\n            </Box>\n          }\n          sx={{\n            '& .MuiAlert-message': {\n              wordBreak: 'break-word',\n              whiteSpace: 'pre-wrap'\n            }\n          }}\n        >\n          <AlertTitle>{title}</AlertTitle>\n          {formatErrorMessage(message)}\n        </Alert>\n      </Collapse>\n    </Box>\n  );\n};\n\nexport default ErrorAlert;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,KAAK,CAAEC,UAAU,CAAEC,GAAG,CAAEC,UAAU,CAAEC,QAAQ,KAAQ,eAAe,CAC5E,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAUtD,QAAS,CAAAC,UAAUA,CAAAC,IAAA,CAMC,IANA,CAClBC,KAAK,CAAG,KAAK,CACbC,OAAO,CACPC,OAAO,CACPC,QAAQ,CAAG,KAAK,CAChBC,SAAS,CAAG,KAAM;AACH,CAAC,CAAAL,IAAA,CAChB,KAAM,CAACM,IAAI,CAAEC,OAAO,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAEtC;AACAC,SAAS,CAAC,IAAM,CACd,GAAI,CAAAqB,KAAqB,CACzB,GAAIJ,QAAQ,EAAIE,IAAI,CAAE,CACpBE,KAAK,CAAGC,UAAU,CAAC,IAAM,CACvBF,OAAO,CAAC,KAAK,CAAC,CAChB,CAAC,CAAEF,SAAS,CAAC,CACf,CAEA,MAAO,IAAM,CACX,GAAIG,KAAK,CAAEE,YAAY,CAACF,KAAK,CAAC,CAChC,CAAC,CACH,CAAC,CAAE,CAACJ,QAAQ,CAAEC,SAAS,CAAEC,IAAI,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAK,kBAAkB,CAAIC,GAAW,EAAK,CAC1C;AACA,GAAIA,GAAG,CAACC,MAAM,CAAG,GAAG,CAAE,CACpB,MAAO,CAAAD,GAAG,CAACE,SAAS,CAAC,CAAC,CAAE,GAAG,CAAC,CAAG,KAAK,CACtC,CACA,MAAO,CAAAF,GAAG,CACZ,CAAC,CAED,mBACEhB,IAAA,CAACN,GAAG,EAACyB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cACjBrB,IAAA,CAACJ,QAAQ,EAAC0B,EAAE,CAAEZ,IAAK,CAAAW,QAAA,cACjBnB,KAAA,CAACV,KAAK,EACJ+B,QAAQ,CAAC,OAAO,CAChBC,MAAM,cACJtB,KAAA,CAACR,GAAG,EAAA2B,QAAA,EACDd,OAAO,eACNP,IAAA,CAACL,UAAU,EACT,aAAW,OAAO,CAClB8B,KAAK,CAAC,SAAS,CACfC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEpB,OAAQ,CACjBF,KAAK,CAAC,mBAAS,CAAAgB,QAAA,cAEfrB,IAAA,CAACF,WAAW,EAAC8B,QAAQ,CAAC,SAAS,CAAE,CAAC,CACxB,CACb,cACD5B,IAAA,CAACL,UAAU,EACT,aAAW,OAAO,CAClB8B,KAAK,CAAC,SAAS,CACfC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAMhB,OAAO,CAAC,KAAK,CAAE,CAAAU,QAAA,cAE9BrB,IAAA,CAACH,SAAS,EAAC+B,QAAQ,CAAC,SAAS,CAAE,CAAC,CACtB,CAAC,EACV,CACN,CACDT,EAAE,CAAE,CACF,qBAAqB,CAAE,CACrBU,SAAS,CAAE,YAAY,CACvBC,UAAU,CAAE,UACd,CACF,CAAE,CAAAT,QAAA,eAEFrB,IAAA,CAACP,UAAU,EAAA4B,QAAA,CAAEhB,KAAK,CAAa,CAAC,CAC/BU,kBAAkB,CAACT,OAAO,CAAC,EACvB,CAAC,CACA,CAAC,CACR,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}