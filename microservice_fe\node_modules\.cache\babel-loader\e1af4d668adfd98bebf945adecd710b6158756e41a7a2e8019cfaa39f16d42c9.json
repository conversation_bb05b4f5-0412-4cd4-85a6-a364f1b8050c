{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{useParams,useNavigate}from'react-router-dom';import{Box,Button,Chip}from'@mui/material';import ArrowBackIcon from'@mui/icons-material/ArrowBack';import EditIcon from'@mui/icons-material/Edit';import{ContractDetails}from'../components/contract';import{ContractStatusMap}from'../models';import{contractService}from'../services/contract/contractService';import{Loading<PERSON>pinner,ErrorAlert,PageHeader}from'../components/common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ContractDetailsPage=()=>{const{id}=useParams();const navigate=useNavigate();const[contract,setContract]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const fetchContract=useCallback(async()=>{if(!id)return;setLoading(true);try{const data=await contractService.getContractById(parseInt(id,10));setContract(data);}catch(err){setError(err.message||'Đã xảy ra lỗi khi tải thông tin hợp đồng');}finally{setLoading(false);}},[id]);useEffect(()=>{fetchContract();},[fetchContract]);// Listen for refresh flag from localStorage (for payment updates)\nuseEffect(()=>{const handleStorageChange=()=>{const needsRefresh=localStorage.getItem('contractsListNeedsRefresh');if(needsRefresh==='true'){// Don't remove the flag here, let ContractsListPage handle it\nfetchContract();// Refresh current contract details\n}};// Listen for storage changes\nwindow.addEventListener('storage',handleStorageChange);// Also listen for focus events (when user returns to tab)\nwindow.addEventListener('focus',handleStorageChange);return()=>{window.removeEventListener('storage',handleStorageChange);window.removeEventListener('focus',handleStorageChange);};},[fetchContract]);const handleBack=()=>{navigate('/contracts');};const handleEdit=()=>{navigate(\"/contracts/edit/\".concat(id));};if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{fullScreen:true});}if(error){return/*#__PURE__*/_jsx(ErrorAlert,{message:error});}if(!contract){return/*#__PURE__*/_jsx(ErrorAlert,{message:\"Kh\\xF4ng t\\xECm th\\u1EA5y h\\u1EE3p \\u0111\\u1ED3ng\"});}const getStatusColor=status=>{switch(status){case 0:// Pending\nreturn'warning';case 1:// Active\nreturn'success';case 2:// Completed\nreturn'info';case 3:// Cancelled\nreturn'error';default:return'default';}};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),onClick:handleBack,children:\"Quay l\\u1EA1i danh s\\xE1ch h\\u1EE3p \\u0111\\u1ED3ng\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(EditIcon,{}),onClick:handleEdit,children:\"Ch\\u1EC9nh s\\u1EEDa h\\u1EE3p \\u0111\\u1ED3ng\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(PageHeader,{title:\"H\\u1EE3p \\u0111\\u1ED3ng #\".concat(contract.id),subtitle:\"Kh\\xE1ch h\\xE0ng: \".concat(contract.customerName)}),/*#__PURE__*/_jsx(Chip,{label:ContractStatusMap[contract.status||0],color:getStatusColor(contract.status||0),sx:{fontSize:'1rem',py:1,px:2}})]}),/*#__PURE__*/_jsx(ContractDetails,{contract:contract})]});};export default ContractDetailsPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "Box", "<PERSON><PERSON>", "Chip", "ArrowBackIcon", "EditIcon", "ContractDetails", "ContractStatusMap", "contractService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "ContractDetailsPage", "id", "navigate", "contract", "setContract", "loading", "setLoading", "error", "setError", "fetchContract", "data", "getContractById", "parseInt", "err", "message", "handleStorageChange", "needsRefresh", "localStorage", "getItem", "window", "addEventListener", "removeEventListener", "handleBack", "handleEdit", "concat", "fullScreen", "getStatusColor", "status", "children", "sx", "display", "justifyContent", "alignItems", "mb", "startIcon", "onClick", "variant", "title", "subtitle", "customerName", "label", "color", "fontSize", "py", "px"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/pages/ContractDetailsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, Button, Chip } from '@mui/material';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport EditIcon from '@mui/icons-material/Edit';\nimport { ContractDetails } from '../components/contract';\nimport { CustomerContract, ContractStatusMap } from '../models';\nimport { contractService } from '../services/contract/contractService';\nimport { Loading<PERSON>pinner, ErrorAlert, PageHeader } from '../components/common';\n\nconst ContractDetailsPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const [contract, setContract] = useState<CustomerContract | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchContract = useCallback(async () => {\n    if (!id) return;\n\n    setLoading(true);\n    try {\n      const data = await contractService.getContractById(parseInt(id, 10));\n      setContract(data);\n    } catch (err: any) {\n      setError(err.message || 'Đã xảy ra lỗi khi tải thông tin hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n\n  useEffect(() => {\n    fetchContract();\n  }, [fetchContract]);\n\n  // Listen for refresh flag from localStorage (for payment updates)\n  useEffect(() => {\n    const handleStorageChange = () => {\n      const needsRefresh = localStorage.getItem('contractsListNeedsRefresh');\n      if (needsRefresh === 'true') {\n        // Don't remove the flag here, let ContractsListPage handle it\n        fetchContract(); // Refresh current contract details\n      }\n    };\n\n    // Listen for storage changes\n    window.addEventListener('storage', handleStorageChange);\n\n    // Also listen for focus events (when user returns to tab)\n    window.addEventListener('focus', handleStorageChange);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('focus', handleStorageChange);\n    };\n  }, [fetchContract]);\n\n  const handleBack = () => {\n    navigate('/contracts');\n  };\n\n  const handleEdit = () => {\n    navigate(`/contracts/edit/${id}`);\n  };\n\n  if (loading) {\n    return <LoadingSpinner fullScreen />;\n  }\n\n  if (error) {\n    return <ErrorAlert message={error} />;\n  }\n\n  if (!contract) {\n    return <ErrorAlert message=\"Không tìm thấy hợp đồng\" />;\n  }\n\n  const getStatusColor = (status: number) => {\n    switch (status) {\n      case 0: // Pending\n        return 'warning';\n      case 1: // Active\n        return 'success';\n      case 2: // Completed\n        return 'info';\n      case 3: // Cancelled\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Button startIcon={<ArrowBackIcon />} onClick={handleBack}>\n          Quay lại danh sách hợp đồng\n        </Button>\n        <Button variant=\"contained\" startIcon={<EditIcon />} onClick={handleEdit}>\n          Chỉnh sửa hợp đồng\n        </Button>\n      </Box>\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <PageHeader\n          title={`Hợp đồng #${contract.id}`}\n          subtitle={`Khách hàng: ${contract.customerName}`}\n        />\n        <Chip\n          label={ContractStatusMap[contract.status || 0]}\n          color={getStatusColor(contract.status || 0)}\n          sx={{ fontSize: '1rem', py: 1, px: 2 }}\n        />\n      </Box>\n\n      <ContractDetails contract={contract} />\n    </Box>\n  );\n};\n\nexport default ContractDetailsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,OAASC,GAAG,CAAEC,MAAM,CAAEC,IAAI,KAAQ,eAAe,CACjD,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,OAASC,eAAe,KAAQ,wBAAwB,CACxD,OAA2BC,iBAAiB,KAAQ,WAAW,CAC/D,OAASC,eAAe,KAAQ,sCAAsC,CACtE,OAASC,cAAc,CAAEC,UAAU,CAAEC,UAAU,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9E,KAAM,CAAAC,mBAA6B,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAAEC,EAAG,CAAC,CAAGlB,SAAS,CAAiB,CAAC,CAC1C,KAAM,CAAAmB,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACmB,QAAQ,CAAEC,WAAW,CAAC,CAAGxB,QAAQ,CAA0B,IAAI,CAAC,CACvE,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAAA6B,aAAa,CAAG3B,WAAW,CAAC,SAAY,CAC5C,GAAI,CAACmB,EAAE,CAAE,OAETK,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAI,IAAI,CAAG,KAAM,CAAAlB,eAAe,CAACmB,eAAe,CAACC,QAAQ,CAACX,EAAE,CAAE,EAAE,CAAC,CAAC,CACpEG,WAAW,CAACM,IAAI,CAAC,CACnB,CAAE,MAAOG,GAAQ,CAAE,CACjBL,QAAQ,CAACK,GAAG,CAACC,OAAO,EAAI,0CAA0C,CAAC,CACrE,CAAC,OAAS,CACRR,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAACL,EAAE,CAAC,CAAC,CAERpB,SAAS,CAAC,IAAM,CACd4B,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,CAACA,aAAa,CAAC,CAAC,CAEnB;AACA5B,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,YAAY,CAAGC,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CACtE,GAAIF,YAAY,GAAK,MAAM,CAAE,CAC3B;AACAP,aAAa,CAAC,CAAC,CAAE;AACnB,CACF,CAAC,CAED;AACAU,MAAM,CAACC,gBAAgB,CAAC,SAAS,CAAEL,mBAAmB,CAAC,CAEvD;AACAI,MAAM,CAACC,gBAAgB,CAAC,OAAO,CAAEL,mBAAmB,CAAC,CAErD,MAAO,IAAM,CACXI,MAAM,CAACE,mBAAmB,CAAC,SAAS,CAAEN,mBAAmB,CAAC,CAC1DI,MAAM,CAACE,mBAAmB,CAAC,OAAO,CAAEN,mBAAmB,CAAC,CAC1D,CAAC,CACH,CAAC,CAAE,CAACN,aAAa,CAAC,CAAC,CAEnB,KAAM,CAAAa,UAAU,CAAGA,CAAA,GAAM,CACvBpB,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAC,CAED,KAAM,CAAAqB,UAAU,CAAGA,CAAA,GAAM,CACvBrB,QAAQ,oBAAAsB,MAAA,CAAoBvB,EAAE,CAAE,CAAC,CACnC,CAAC,CAED,GAAII,OAAO,CAAE,CACX,mBAAOR,IAAA,CAACJ,cAAc,EAACgC,UAAU,MAAE,CAAC,CACtC,CAEA,GAAIlB,KAAK,CAAE,CACT,mBAAOV,IAAA,CAACH,UAAU,EAACoB,OAAO,CAAEP,KAAM,CAAE,CAAC,CACvC,CAEA,GAAI,CAACJ,QAAQ,CAAE,CACb,mBAAON,IAAA,CAACH,UAAU,EAACoB,OAAO,CAAC,mDAAyB,CAAE,CAAC,CACzD,CAEA,KAAM,CAAAY,cAAc,CAAIC,MAAc,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,EAAC,CAAE;AACN,MAAO,SAAS,CAClB,IAAK,EAAC,CAAE;AACN,MAAO,SAAS,CAClB,IAAK,EAAC,CAAE;AACN,MAAO,MAAM,CACf,IAAK,EAAC,CAAE;AACN,MAAO,OAAO,CAChB,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED,mBACE5B,KAAA,CAACd,GAAG,EAAA2C,QAAA,eACF7B,KAAA,CAACd,GAAG,EAAC4C,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACzF/B,IAAA,CAACX,MAAM,EAACgD,SAAS,cAAErC,IAAA,CAACT,aAAa,GAAE,CAAE,CAAC+C,OAAO,CAAEb,UAAW,CAAAM,QAAA,CAAC,oDAE3D,CAAQ,CAAC,cACT/B,IAAA,CAACX,MAAM,EAACkD,OAAO,CAAC,WAAW,CAACF,SAAS,cAAErC,IAAA,CAACR,QAAQ,GAAE,CAAE,CAAC8C,OAAO,CAAEZ,UAAW,CAAAK,QAAA,CAAC,6CAE1E,CAAQ,CAAC,EACN,CAAC,cAEN7B,KAAA,CAACd,GAAG,EAAC4C,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACzF/B,IAAA,CAACF,UAAU,EACT0C,KAAK,6BAAAb,MAAA,CAAerB,QAAQ,CAACF,EAAE,CAAG,CAClCqC,QAAQ,sBAAAd,MAAA,CAAiBrB,QAAQ,CAACoC,YAAY,CAAG,CAClD,CAAC,cACF1C,IAAA,CAACV,IAAI,EACHqD,KAAK,CAAEjD,iBAAiB,CAACY,QAAQ,CAACwB,MAAM,EAAI,CAAC,CAAE,CAC/Cc,KAAK,CAAEf,cAAc,CAACvB,QAAQ,CAACwB,MAAM,EAAI,CAAC,CAAE,CAC5CE,EAAE,CAAE,CAAEa,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CACxC,CAAC,EACC,CAAC,cAEN/C,IAAA,CAACP,eAAe,EAACa,QAAQ,CAAEA,QAAS,CAAE,CAAC,EACpC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}